import { NextRequest, NextResponse } from 'next/server';
import { AI<PERSON>rovider } from '@/types';
import { executeQuery } from '@/lib/db';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'your-32-character-secret-key-here';
const ALGORITHM = 'aes-256-cbc';

// 解密函数
function decrypt(text: string): string {
  if (!text) return '';
  try {
    const textParts = text.split(':');
    if (textParts.length !== 2) return text;

    const iv = Buffer.from(textParts[0], 'hex');
    const encryptedText = textParts[1];
    const decipher = crypto.createDecipheriv(ALGORITHM, Buffer.from(ENCRYPTION_KEY.slice(0, 32)), iv);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch (error) {
    console.error('解密失败:', error);
    return text;
  }
}

// JWT验证中间件
const verifyToken = (request: NextRequest) => {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    return decoded;
  } catch (error) {
    return null;
  }
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { provider, config, messages, content, files, agent } = body;

    // 验证用户身份
    const user = verifyToken(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      );
    }

    // 从数据库获取用户的API配置（包含加密的API密钥）
    const configQuery = `
      SELECT api_key, base_url, default_model, temperature, max_tokens, extra_config
      FROM ai_provider_configs
      WHERE user_id = ? AND provider = ? AND enabled = true
      LIMIT 1
    `;

    const configs = await executeQuery<any[]>(configQuery, [user.userId, provider]);

    if (configs.length === 0) {
      return NextResponse.json(
        { success: false, error: `${provider} 服务未配置或未启用，请先在设置中配置 API Key` },
        { status: 400 }
      );
    }

    const dbConfig = configs[0];

    // 解密API密钥并构建完整配置
    const fullConfig = {
      ...config, // 前端传来的非敏感配置
      apiKey: decrypt(dbConfig.api_key || ''), // 从数据库解密获取API密钥
      baseUrl: config.baseUrl || dbConfig.base_url,
      model: config.model || dbConfig.default_model,
      temperature: config.temperature || dbConfig.temperature,
      maxTokens: config.maxTokens || dbConfig.max_tokens,
    };

    // 验证API密钥是否存在
    if (!fullConfig.apiKey) {
      return NextResponse.json(
        { success: false, error: `${provider} API密钥未配置，请先在设置中配置 API Key` },
        { status: 400 }
      );
    }

    switch (provider) {
      case 'openai':
        return await handleOpenAI(fullConfig, messages, content);
      case 'google':
        return await handleGoogle(fullConfig, messages, content);
      case 'anthropic':
        return await handleAnthropic(fullConfig, messages, content);
      case 'deepseek':
        return await handleDeepSeek(fullConfig, messages, content);
      default:
        return NextResponse.json(
          { success: false, error: `不支持的 AI 提供商: ${provider}` },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('AI Chat API 错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

async function handleOpenAI(config: any, messages: any[], content: string) {
  // 确保 baseUrl 包含 /v1
  let baseUrl = config.baseUrl || 'https://api.openai.com/v1';
  if (!baseUrl.includes('/v1')) {
    baseUrl = baseUrl.replace(/\/$/, '') + '/v1';
  }
  const url = `${baseUrl}/chat/completions`;
  
  const requestBody = {
    model: config.model,
    messages: messages,
    temperature: parseFloat(config.temperature) || 0.7,
    max_tokens: parseInt(config.maxTokens) || 4096,
  };

  console.log('OpenAI 请求:', { url, model: config.model });

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.apiKey}`,
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    console.error('OpenAI API 错误:', errorData);
    return NextResponse.json(
      { 
        success: false, 
        error: errorData.error?.message || `OpenAI API 错误: ${response.status}` 
      },
      { status: response.status }
    );
  }

  const data = await response.json();
  return NextResponse.json({
    success: true,
    data: {
      content: data.choices[0]?.message?.content || '',
      usage: data.usage,
    },
  });
}

async function handleGoogle(config: any, messages: any[], content: string) {
  // 处理 baseUrl，避免路径重复
  let baseUrl = config.baseUrl || 'https://generativelanguage.googleapis.com';

  // 移除末尾的斜杠
  if (baseUrl.endsWith('/')) {
    baseUrl = baseUrl.slice(0, -1);
  }

  // 智能处理 URL 路径，兼容标准 API 和代理 API
  let url;
  let isProxyAPI = false;

  if (baseUrl.includes('/v1beta')) {
    // 如果已经包含 v1beta 路径，直接添加 models 部分
    url = `${baseUrl}/models/${config.model}:generateContent?key=${config.apiKey}`;
  } else if (baseUrl.includes('/v1') && !baseUrl.includes('/v1beta')) {
    // 如果包含 /v1（如代理 API），使用 OpenAI 兼容格式
    url = `${baseUrl}/chat/completions`;
    isProxyAPI = true;
  } else {
    // 标准情况，添加完整的 v1beta/models 路径
    url = `${baseUrl}/v1beta/models/${config.model}:generateContent?key=${config.apiKey}`;
  }

  // 根据 API 类型选择不同的请求体格式
  let requestBody;
  let headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (isProxyAPI) {
    // 代理 API 使用 OpenAI 兼容格式
    requestBody = {
      model: config.model,
      messages: messages.map((msg: any) => ({
        role: msg.role,
        content: msg.content
      })),
      temperature: parseFloat(config.temperature) || 0.7,
      max_tokens: parseInt(config.maxTokens) || 4096,
    };
    headers['Authorization'] = `Bearer ${config.apiKey}`;
  } else {
    // 标准 Google API 格式
    requestBody = {
      contents: messages.map((msg: any) => ({
        role: msg.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: msg.content }]
      })),
      generationConfig: {
        temperature: parseFloat(config.temperature) || 0.7,
        maxOutputTokens: parseInt(config.maxTokens) || 4096,
      }
    };
  }



  const response = await fetch(url, {
    method: 'POST',
    headers,
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));

    return NextResponse.json(
      { 
        success: false, 
        error: errorData.error?.message || `Google API 错误: ${response.status}` 
      },
      { status: response.status }
    );
  }

  const data = await response.json();
  console.log('Google API 响应:', JSON.stringify(data, null, 2));

  // 根据 API 类型解析不同的响应格式
  let responseContent;
  if (isProxyAPI) {
    // 代理 API 使用 OpenAI 兼容格式
    responseContent = data.choices?.[0]?.message?.content || '';
  } else {
    // 标准 Google API 格式
    responseContent = data.candidates?.[0]?.content?.parts?.[0]?.text || '';
  }

  console.log('提取的内容:', responseContent.substring(0, 200) + '...');
  return NextResponse.json({
    success: true,
    data: {
      content: responseContent,
      usage: data.usage,
    },
  });
}

async function handleAnthropic(config: any, messages: any[], content: string) {
  // 处理 baseUrl，兼容标准 API 和代理 API
  let baseUrl = config.baseUrl || 'https://api.anthropic.com';

  // 移除末尾的斜杠
  if (baseUrl.endsWith('/')) {
    baseUrl = baseUrl.slice(0, -1);
  }

  // 智能处理 URL 路径，兼容标准 API 和代理 API
  let url;
  let isProxyAPI = false;

  if (baseUrl.includes('/v1') && !baseUrl.includes('/v1/messages')) {
    // 如果包含 /v1（如代理 API），使用 OpenAI 兼容格式
    url = `${baseUrl}/chat/completions`;
    isProxyAPI = true;
  } else {
    // 标准 Anthropic API 格式
    url = `${baseUrl}/v1/messages`;
  }

  // 根据 API 类型选择不同的请求体格式
  let requestBody;
  let headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${config.apiKey}`,
  };

  if (isProxyAPI) {
    // 代理 API 使用 OpenAI 兼容格式
    requestBody = {
      model: config.model,
      messages: messages,
      temperature: parseFloat(config.temperature) || 0.7,
      max_tokens: parseInt(config.maxTokens) || 4096,
    };
  } else {
    // 标准 Anthropic API 格式
    requestBody = {
      model: config.model,
      max_tokens: parseInt(config.maxTokens) || 4096,
      messages: messages,
      temperature: parseFloat(config.temperature) || 0.7,
    };
    headers['anthropic-version'] = '2023-06-01';
  }



  const response = await fetch(url, {
    method: 'POST',
    headers,
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    console.error('Anthropic API 错误:', errorData);
    return NextResponse.json(
      { 
        success: false, 
        error: errorData.error?.message || `Anthropic API 错误: ${response.status}` 
      },
      { status: response.status }
    );
  }

  const data = await response.json();

  // 根据 API 类型解析不同的响应格式
  let responseContent;
  if (isProxyAPI) {
    // 代理 API 使用 OpenAI 兼容格式
    responseContent = data.choices?.[0]?.message?.content || '';
  } else {
    // 标准 Anthropic API 格式
    responseContent = data.content?.[0]?.text || '';
  }

  return NextResponse.json({
    success: true,
    data: {
      content: responseContent,
      usage: data.usage,
    },
  });
}

async function handleDeepSeek(config: any, messages: any[], content: string) {
  const url = `${config.baseUrl || 'https://api.deepseek.com'}/v1/chat/completions`;

  const requestBody = {
    model: config.model,
    messages: messages,
    temperature: parseFloat(config.temperature) || 0.7,
    max_tokens: parseInt(config.maxTokens) || 4096,
  };



  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.apiKey}`,
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('DeepSeek API 错误:', response.status, errorText);

    let errorData: any = {};
    try {
      errorData = JSON.parse(errorText);
    } catch (e) {

    }

    return NextResponse.json(
      {
        success: false,
        error: errorData.error?.message || errorData.message || errorText || `DeepSeek API 错误: ${response.status}`
      },
      { status: response.status }
    );
  }

  const data = await response.json();
  return NextResponse.json({
    success: true,
    data: {
      content: data.choices[0]?.message?.content || '',
      usage: data.usage,
    },
  });
}

