import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import nodemailer from 'nodemailer';
import crypto from 'crypto';

// 邮箱验证正则
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// 生成重置密码token
const generateResetToken = () => {
  return crypto.randomBytes(32).toString('hex');
};

// 创建邮件发送器
const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.SMTP_HOST || 'smtp.qq.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });
};

// 发送重置密码邮件
const sendResetPasswordEmail = async (email: string, resetToken: string, baseUrl: string) => {
  const transporter = createTransporter();
  const resetUrl = `${baseUrl}/auth/reset-password?token=${resetToken}`;
  
  const mailOptions = {
    from: `"微甜 AI Studio" <${process.env.SMTP_USER}>`,
    to: email,
    subject: '重置密码 - 微甜 AI Studio',
    html: `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #1890ff; margin: 0;">微甜 AI Studio</h1>
          <p style="color: #666; margin: 10px 0;">让创意无限可能</p>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
          <h2 style="color: #333; margin-bottom: 20px;">重置密码</h2>
          <p style="color: #666; margin-bottom: 20px;">您请求重置微甜 AI Studio 账户的密码。请点击下面的按钮来重置您的密码：</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" style="background: #1890ff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
              重置密码
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px; margin-bottom: 10px;">
            如果按钮无法点击，请复制以下链接到浏览器地址栏：
          </p>
          <p style="color: #1890ff; font-size: 14px; word-break: break-all; background: #fff; padding: 10px; border-radius: 4px;">
            ${resetUrl}
          </p>
          
          <p style="color: #999; font-size: 14px; margin-top: 30px;">
            此链接有效期为 1 小时，请及时使用。<br>
            如果您没有申请重置密码，请忽略此邮件。
          </p>
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #999; font-size: 12px;">
          <p>此邮件由系统自动发送，请勿回复。</p>
          <p>© 2025 微甜 AI Studio. All rights reserved.</p>
        </div>
      </div>
    `,
  };

  await transporter.sendMail(mailOptions);
};

// POST - 发送重置密码邮件
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = body;

    // 自动获取当前请求的域名
    const host = request.headers.get('host');
    const protocol = request.headers.get('x-forwarded-proto') ||
                    (host?.includes('localhost') ? 'http' : 'https');
    const baseUrl = `${protocol}://${host}`;

    // 基础验证
    if (!email) {
      return NextResponse.json(
        {
          success: false,
          error: '邮箱地址是必填的',
        },
        { status: 400 }
      );
    }

    // 邮箱格式验证
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        {
          success: false,
          error: '邮箱格式不正确',
        },
        { status: 400 }
      );
    }

    // 检查邮箱是否已注册
    const existingUser = await executeQuery(
      'SELECT id, username FROM users WHERE email = ?',
      [email]
    );

    if (existingUser.length === 0) {
      // 为了安全考虑，即使邮箱不存在也返回成功消息
      return NextResponse.json({
        success: true,
        message: '如果该邮箱已注册，重置密码链接已发送到您的邮箱',
      });
    }

    const user = existingUser[0];

    // 生成重置token
    const resetToken = generateResetToken();
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1小时后过期

    // 删除该用户之前的重置密码记录
    await executeQuery(
      'DELETE FROM password_reset_tokens WHERE user_id = ?',
      [user.id]
    );

    // 保存重置token到数据库
    await executeQuery(
      'INSERT INTO password_reset_tokens (user_id, token, expires_at, created_at) VALUES (?, ?, ?, ?)',
      [user.id, resetToken, expiresAt, new Date()]
    );

    // 发送重置密码邮件
    try {
      // 在开发环境中，同时输出到控制台和发送邮件（如果配置了SMTP）
      if (process.env.NODE_ENV === 'development') {
        const resetUrl = `${baseUrl}/auth/reset-password?token=${resetToken}`;
        console.log(`\n=== 重置密码链接 ===`);
        console.log(`邮箱: ${email}`);
        console.log(`用户: ${user.username}`);
        console.log(`重置链接: ${resetUrl}`);
        console.log(`有效期: 1小时`);
        console.log(`=====================\n`);

        // 如果配置了SMTP，也尝试发送邮件
        if (process.env.SMTP_USER && process.env.SMTP_PASS &&
            process.env.SMTP_USER !== '<EMAIL>') {
          try {
            await sendResetPasswordEmail(email, resetToken, baseUrl);
            console.log('重置密码邮件发送成功！');
          } catch (emailError) {
            console.log('邮件发送失败，但重置链接已在控制台输出');
            console.error('邮件发送错误详情:', emailError);
          }
        }
      } else {
        await sendResetPasswordEmail(email, resetToken, baseUrl);
      }
    } catch (emailError) {
      console.error('发送邮件失败:', emailError);
      return NextResponse.json(
        {
          success: false,
          error: '发送重置密码邮件失败，请稍后重试',
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '重置密码链接已发送到您的邮箱，请查收',
    });
  } catch (error) {
    console.error('发送重置密码邮件失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '发送重置密码邮件失败，请重试',
      },
      { status: 500 }
    );
  }
}
