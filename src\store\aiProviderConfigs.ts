import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AIProviderConfig, AIProvider, UserSettings } from '@/types/ai-provider';
import { aiProvidersApi, userSettingsApi } from '@/lib/api';
import { useAuthStore } from '@/store/auth';

interface AIProviderConfigsState {
  // 状态
  configs: AIProviderConfig[];
  userSettings: UserSettings | null;
  loading: boolean;
  error: string | null;

  // 操作
  loadConfigs: (userId: string, forceGlobal?: boolean) => Promise<void>;
  loadGlobalConfigs: () => Promise<void>;
  saveConfig: (config: Omit<AIProviderConfig, 'id' | 'createdAt' | 'updatedAt' | 'hasApiKey'> & { apiKey?: string }) => Promise<void>;
  deleteConfig: (userId: string, provider: AIProvider) => Promise<void>;
  loadUserSettings: (userId: string) => Promise<void>;
  saveUserSettings: (settings: Partial<UserSettings> & { userId: string }) => Promise<void>;
  updateCurrentModel: (provider: AIProvider, model: string) => Promise<void>;
  getProviderConfig: (provider: AIProvider) => AIProviderConfig | null;
  isProviderEnabled: (provider: AIProvider) => boolean;
  clearError: () => void;
}

export const useAIProviderConfigsStore = create<AIProviderConfigsState>()(
  persist(
    (set, get) => ({
      // 初始状态
      configs: [],
      userSettings: null,
      loading: false,
      error: null,

      // 加载用户的AI提供商配置
      loadConfigs: async (userId: string, forceGlobal: boolean = false) => {
        const { loading } = get();
        if (loading) return; // 防止重复加载

        set({ loading: true, error: null });
        try {
          // 如果强制使用全局配置，直接加载全局配置
          if (forceGlobal) {
            const globalResponse = await aiProvidersApi.getGlobalConfigs();
            if (globalResponse.success && globalResponse.data) {
              set({ configs: globalResponse.data, loading: false });
            } else {
              set({ configs: [], loading: false });
            }
            return;
          }

          // 先尝试加载用户特定的配置（用于管理员）
          const userResponse = await aiProvidersApi.getConfigs(userId);

          // 如果用户没有配置，则加载全局配置
          if (!userResponse.success || !userResponse.data || userResponse.data.length === 0) {
            const globalResponse = await aiProvidersApi.getGlobalConfigs();
            if (globalResponse.success && globalResponse.data) {
              set({ configs: globalResponse.data, loading: false });
            } else {
              set({ configs: [], loading: false });
            }
          } else {
            set({ configs: userResponse.data, loading: false });
          }
        } catch (error) {
          console.error('加载AI提供商配置失败:', error);
          set({ error: '加载配置失败', loading: false });
        }
      },

      // 加载全局配置（供普通用户使用）
      loadGlobalConfigs: async () => {
        const { loading } = get();
        if (loading) return; // 防止重复加载

        set({ loading: true, error: null });
        try {
          const response = await aiProvidersApi.getGlobalConfigs();
          if (response.success && response.data) {
            set({ configs: response.data, loading: false });
          } else {
            set({ configs: [], loading: false });
          }
        } catch (error) {
          console.error('加载全局AI提供商配置失败:', error);
          set({ error: '加载配置失败', loading: false });
        }
      },

      // 保存AI提供商配置
      saveConfig: async (config) => {
        set({ loading: true, error: null });
        try {
          const response = await aiProvidersApi.saveConfig(config);
          if (response.success) {
            // 直接更新本地状态，避免重新加载导致页面刷新
            const { configs } = get();
            const existingIndex = configs.findIndex(c => c.provider === config.provider && c.userId === config.userId);

            const newConfig = {
              id: existingIndex >= 0 ? configs[existingIndex].id : `${config.userId}-${config.provider}`,
              ...config,
              hasApiKey: Boolean(config.apiKey), // 根据是否提供了apiKey设置hasApiKey
              createdAt: existingIndex >= 0 ? configs[existingIndex].createdAt : new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            };

            // 从配置中移除apiKey字段，因为它不应该存储在前端状态中
            const { apiKey, ...configWithoutApiKey } = newConfig;

            if (existingIndex >= 0) {
              // 更新现有配置
              const updatedConfigs = [...configs];
              updatedConfigs[existingIndex] = configWithoutApiKey;
              set({ configs: updatedConfigs, loading: false });
            } else {
              // 添加新配置
              set({ configs: [...configs, configWithoutApiKey], loading: false });
            }
          } else {
            set({ error: response.error || '保存配置失败', loading: false });
          }
        } catch (error) {
          console.error('保存AI提供商配置失败:', error);
          set({ error: '保存配置失败', loading: false });
        }
      },

      // 删除AI提供商配置
      deleteConfig: async (userId: string, provider: AIProvider) => {
        set({ loading: true, error: null });
        try {
          const response = await aiProvidersApi.deleteConfig(userId, provider);
          if (response.success) {
            // 重新加载配置
            await get().loadConfigs(userId);
          } else {
            set({ error: response.error || '删除配置失败', loading: false });
          }
        } catch (error) {
          console.error('删除AI提供商配置失败:', error);
          set({ error: '删除配置失败', loading: false });
        }
      },

      // 加载用户设置
      loadUserSettings: async (userId: string) => {
        set({ loading: true, error: null });
        try {
          const response = await userSettingsApi.getSettings(userId);
          if (response.success && response.data) {
            set({ userSettings: response.data, loading: false });
          } else {
            set({ error: response.error || '加载用户设置失败', loading: false });
          }
        } catch (error) {
          console.error('加载用户设置失败:', error);
          set({ error: '加载用户设置失败', loading: false });
        }
      },

      // 保存用户设置
      saveUserSettings: async (settings) => {
        set({ loading: true, error: null });
        try {
          const response = await userSettingsApi.saveSettings(settings);
          if (response.success) {
            // 直接更新本地状态，避免重新加载导致页面刷新
            const { userSettings: currentSettings } = get();
            if (currentSettings) {
              const updatedSettings = {
                ...currentSettings,
                ...settings,
                updatedAt: new Date().toISOString(),
              };
              set({ userSettings: updatedSettings, loading: false });
            } else {
              // 如果没有当前设置，创建新的设置对象
              const newSettings = {
                id: `${settings.userId}-settings`,
                userId: settings.userId,
                currentProvider: settings.currentProvider || 'google',
                currentModel: settings.currentModel || 'gemini-2.0-flash-exp',
                theme: settings.theme || 'light',
                language: settings.language || 'zh-CN',
                fontSize: settings.fontSize || 'medium',
                autoSave: settings.autoSave ?? true,
                notifications: settings.notifications ?? true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              };
              set({ userSettings: newSettings, loading: false });
            }
          } else {
            set({ error: response.error || '保存用户设置失败', loading: false });
          }
        } catch (error) {
          console.error('保存用户设置失败:', error);
          set({ error: '保存用户设置失败', loading: false });
        }
      },

      // 更新当前模型
      updateCurrentModel: async (provider: AIProvider, model: string) => {
        const { userSettings } = get();
        const authUser = useAuthStore.getState().user;
        const userId = userSettings?.userId || authUser?.id;

        if (userId) {
          await get().saveUserSettings({
            userId,
            currentProvider: provider,
            currentModel: model,
          });
        } else {
          console.error('无法更新当前模型：缺少用户ID');
        }
      },

      // 获取指定提供商的配置
      getProviderConfig: (provider: AIProvider) => {
        const { configs } = get();
        return configs.find(config => config.provider === provider) || null;
      },

      // 检查提供商是否启用
      isProviderEnabled: (provider: AIProvider) => {
        const config = get().getProviderConfig(provider);
        return config ? config.enabled : false;
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'ai-provider-configs-storage',
      // 只持久化用户设置，配置信息从数据库加载
      partialize: (state) => ({
        userSettings: state.userSettings,
      }),
    }
  )
);
