// AI提供商配置相关类型定义

export interface AIProviderConfig {
  id: string;
  userId: string;
  provider: AIProvider;
  enabled: boolean;
  hasApiKey: boolean; // 只返回是否有API密钥的状态，不返回实际密钥
  baseUrl?: string;
  defaultModel: string;
  temperature: number;
  maxTokens: number;
  extraConfig?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export type AIProvider = 'openai' | 'anthropic' | 'google' | 'deepseek';

export interface AIProviderInfo {
  key: AIProvider;
  name: string;
  icon: string;
  description: string;
  defaultBaseUrl: string;
  models: AIModel[];
}

export interface AIModel {
  id: string;
  name: string;
  description?: string;
  maxTokens: number;
  inputPrice: number;  // 每1K tokens的价格（美元）
  outputPrice: number; // 每1K tokens的价格（美元）
  supportedFeatures: ModelFeature[];
}

export type ModelFeature = 'text' | 'image' | 'audio' | 'video' | 'function_calling' | 'json_mode';

export interface UserSettings {
  id: string;
  userId: string;
  currentProvider: AIProvider;
  currentModel: string;
  theme: 'light' | 'dark' | 'auto';
  language: string;
  fontSize: 'small' | 'medium' | 'large';
  autoSave: boolean;
  notifications: boolean;
  createdAt: string;
  updatedAt: string;
}

// API响应类型
export interface AIProviderConfigResponse {
  success: boolean;
  data?: AIProviderConfig[];
  error?: string;
}

export interface UserSettingsResponse {
  success: boolean;
  data?: UserSettings;
  error?: string;
}

// 表单数据类型
export interface AIProviderConfigForm {
  provider: AIProvider;
  enabled: boolean;
  apiKey?: string; // 表单中的API密钥是可选的，只有在更新时才需要
  baseUrl?: string;
  defaultModel: string;
  temperature: number;
  maxTokens: number;
  extraConfig?: Record<string, any>;
}

export interface UserSettingsForm {
  currentProvider: AIProvider;
  currentModel: string;
  theme: 'light' | 'dark' | 'auto';
  language: string;
  fontSize: 'small' | 'medium' | 'large';
  autoSave: boolean;
  notifications: boolean;
}

// 预定义的AI提供商信息
export const AI_PROVIDERS: Record<AIProvider, AIProviderInfo> = {
  openai: {
    key: 'openai',
    name: 'OpenAI',
    icon: '🤖',
    description: 'OpenAI的GPT系列模型，包括GPT-4o、o1等最新模型',
    defaultBaseUrl: 'https://api.openai.com/v1',
    models: [
      {
        id: 'gpt-4o-2024-11-20',
        name: 'GPT-4o (2024-11-20)',
        description: '最新版本的GPT-4o，支持多模态',
        maxTokens: 128000,
        inputPrice: 0.0025,
        outputPrice: 0.01,
        supportedFeatures: ['text', 'image', 'audio', 'function_calling', 'json_mode'],
      },
      {
        id: 'gpt-4o',
        name: 'GPT-4o',
        description: '多模态模型，支持文本、图像和音频',
        maxTokens: 128000,
        inputPrice: 0.005,
        outputPrice: 0.015,
        supportedFeatures: ['text', 'image', 'audio', 'function_calling', 'json_mode'],
      },
      {
        id: 'gpt-4o-mini',
        name: 'GPT-4o Mini',
        description: '轻量级版本，更快更便宜',
        maxTokens: 128000,
        inputPrice: 0.00015,
        outputPrice: 0.0006,
        supportedFeatures: ['text', 'image', 'function_calling', 'json_mode'],
      },
    ],
  },
  anthropic: {
    key: 'anthropic',
    name: 'Anthropic',
    icon: '🧠',
    description: 'Anthropic的Claude系列模型，擅长对话和分析',
    defaultBaseUrl: 'https://api.anthropic.com',
    models: [
      {
        id: 'claude-sonnet-4-20250514',
        name: 'Claude Sonnet 4',
        description: '最新的Claude Sonnet 4模型，具有更强的推理能力',
        maxTokens: 200000,
        inputPrice: 0.015,
        outputPrice: 0.075,
        supportedFeatures: ['text', 'image', 'function_calling', 'json_mode'],
      },
      {
        id: 'claude-3-7-sonnet-20250219-thinking',
        name: 'Claude 3.7',
        description: 'Claude 3.7模型，在3.5基础上进一步优化',
        maxTokens: 200000,
        inputPrice: 0.005,
        outputPrice: 0.025,
        supportedFeatures: ['text', 'image', 'function_calling', 'json_mode'],
      },
    ],
  },
  google: {
    key: 'google',
    name: 'Google',
    icon: '🔍',
    description: 'Google的Gemini系列模型，支持多模态输入',
    defaultBaseUrl: 'https://generativelanguage.googleapis.com',
    models: [
      {
        id: 'gemini-2.5-pro',
        name: 'Gemini 2.5 Pro',
        description: '最强大的思维模型',
        maxTokens: 2097152,
        inputPrice: 0.00125,
        outputPrice: 0.005,
        supportedFeatures: ['text', 'image', 'audio', 'video', 'function_calling'],
      },
      {
        id: 'gemini-2.5-flash-preview-05-20',
        name: 'Gemini 2.5 Flash Preview',
        description: '价格性能最佳的模型预览版',
        maxTokens: 1048576,
        inputPrice: 0.000075,
        outputPrice: 0.0003,
        supportedFeatures: ['text', 'image', 'audio', 'video', 'function_calling'],
      },
    ],
  },
  deepseek: {
    key: 'deepseek',
    name: 'DeepSeek',
    icon: '🚀',
    description: 'DeepSeek的高性能模型，专注于推理和编程',
    defaultBaseUrl: 'https://api.deepseek.com',
    models: [
      {
        id: 'deepseek-chat',
        name: 'DeepSeek Chat V3',
        description: '最新的DeepSeek V3对话模型，性能大幅提升',
        maxTokens: 64000,
        inputPrice: 0.00027,
        outputPrice: 0.0011,
        supportedFeatures: ['text', 'function_calling', 'json_mode'],
      },

      {
        id: 'deepseek-coder',
        name: 'DeepSeek Coder',
        description: '专业编程模型',
        maxTokens: 32768,
        inputPrice: 0.00014,
        outputPrice: 0.00028,
        supportedFeatures: ['text', 'function_calling'],
      },
    ],
  },
};
