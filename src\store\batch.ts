import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { BatchItem, BatchTemplate, BatchState } from '@/types';
import { getGeminiService } from '@/services/gemini';
import { Document, Packer, Paragraph, TextRun } from 'docx';
import { AIProvider } from '@/types/ai-provider';
import aiService from '@/services/aiService';

interface BatchStore extends BatchState {
  // Actions
  addBatchItem: (item: Omit<BatchItem, 'id' | 'createdAt'>) => void;
  updateBatchItem: (id: string, updates: Partial<BatchItem>) => void;
  removeBatchItem: (id: string) => void;
  clearBatchItems: () => void;
  
  // Templates
  addTemplate: (template: Omit<BatchTemplate, 'id' | 'createdAt'>) => void;
  updateTemplate: (id: string, updates: Partial<BatchTemplate>) => void;
  removeTemplate: (id: string) => void;
  
  // Generation
  generateBatch: (provider?: AIProvider, model?: string) => Promise<void>;
  generateSingle: (id: string) => Promise<void>;
  stopGeneration: () => void;
  pauseGeneration: () => void;
  resumeGeneration: () => Promise<void>;

  // Batch text processing
  addBatchTexts: (prompt: string, texts: string, clearExisting?: boolean) => void;

  // Export
  exportResults: (format: 'json' | 'csv' | 'txt') => void;
  exportBatchResults: (format: 'txt' | 'docx', scriptType: 'standard' | 'director' | 'combined') => Promise<void>;
  
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setProgress: (progress: { current: number; total: number }) => void;
}

// 生成唯一 ID
const generateId = () => Math.random().toString(36).substr(2, 9);

// 默认模板
const DEFAULT_TEMPLATES: BatchTemplate[] = [
  {
    id: 'marketing-copy',
    name: '营销文案',
    description: '生成产品营销文案',
    prompt: '为以下产品生成一段吸引人的营销文案：{product}',
    variables: ['product'],
    category: 'marketing',
    createdAt: Date.now(),
  },
  {
    id: 'social-media',
    name: '社交媒体',
    description: '生成社交媒体帖子',
    prompt: '为以下主题创建一个引人入胜的社交媒体帖子：{topic}，目标受众：{audience}',
    variables: ['topic', 'audience'],
    category: 'social',
    createdAt: Date.now(),
  },
  {
    id: 'email-subject',
    name: '邮件主题',
    description: '生成邮件主题行',
    prompt: '为以下邮件内容生成5个吸引人的主题行：{content}',
    variables: ['content'],
    category: 'email',
    createdAt: Date.now(),
  },
];

export const useBatchStore = create<BatchStore>()(
  persist(
    (set, get) => ({
      // Initial state
      items: [],
      templates: DEFAULT_TEMPLATES,
      isGenerating: false,
      isPaused: false,
      currentBatchIndex: 0,
      progress: { current: 0, total: 0 },
      loading: false,
      error: null,
      abortController: null,

      // Batch items management
      addBatchItem: (item) => {
        const newItem: BatchItem = {
          ...item,
          id: generateId(),
          createdAt: Date.now(),
        };
        set((state) => ({
          items: [...state.items, newItem],
        }));
      },

      updateBatchItem: (id, updates) => {
        set((state) => ({
          items: state.items.map(item =>
            item.id === id ? { ...item, ...updates } : item
          ),
        }));
      },

      removeBatchItem: (id) => {
        set((state) => ({
          items: state.items.filter(item => item.id !== id),
        }));
      },

      clearBatchItems: () => {
        set({ items: [] });
      },

      // Templates management
      addTemplate: (template) => {
        const newTemplate: BatchTemplate = {
          ...template,
          id: generateId(),
          createdAt: Date.now(),
        };
        set((state) => ({
          templates: [...state.templates, newTemplate],
        }));
      },

      updateTemplate: (id, updates) => {
        set((state) => ({
          templates: state.templates.map(template =>
            template.id === id ? { ...template, ...updates } : template
          ),
        }));
      },

      removeTemplate: (id) => {
        set((state) => ({
          templates: state.templates.filter(template => template.id !== id),
        }));
      },

      // Generation
      generateBatch: async (provider: AIProvider = 'google', model?: string) => {
        const { items } = get();
        const pendingItems = items.filter(item => item.status === 'pending');

        if (pendingItems.length === 0) {
          set({ error: '没有待生成的项目' });
          return;
        }

        // 创建新的AbortController
        const abortController = new AbortController();

        set({
          isGenerating: true,
          error: null,
          progress: { current: 0, total: pendingItems.length },
          abortController: abortController
        });

        let completed = 0;

        for (const item of pendingItems) {
          // 在每个步骤都检查是否被停止
          if (!get().isGenerating) {
            console.log('批量生成已被停止');
            break;
          }

          // 更新项目状态为生成中
          get().updateBatchItem(item.id, {
            status: 'generating',
            startTime: Date.now()
          });

          try {
            // 再次检查是否被停止
            if (!get().isGenerating) {
              get().updateBatchItem(item.id, {
                status: 'pending', // 重置为待生成状态
              });
              break;
            }

            // 构建完整的提示词
            let finalPrompt = item.prompt;
            if (item.variables) {
              Object.entries(item.variables).forEach(([key, value]) => {
                finalPrompt = finalPrompt.replace(new RegExp(`{${key}}`, 'g'), value);
              });
            }

            // 定义三种剧本格式的提示词
            const standardScriptPrompt = `${finalPrompt}

请将以下内容改写为标准剧本格式：

剧本格式说明：
1.严格遵循 "场号 - 场景 - 时间" 三要素标题
2.环境、动作描写使用△符号强调，独立成段
3.台词对话独立成段，标明说话人，说话人和台词使用字体加粗强调
4.人物首次出场标注年龄和身份、外貌，使用△符号强调
5.环境 、人物 、 动作不要分层描写，环境描写融入动作段落，不单独分层。动作描写融入叙事，不单独分层
6.不需要展现镜头运动
7.转场明确提示下一场戏的衔接点

内容：${item.input || ''}`;

            const directorScriptPrompt = `${finalPrompt}

请将以下内容改写为导演版剧本格式：

剧本格式说明：
1.严格遵循 "场号 - 场景 - 时间" 三要素标题
2.环境、动作描写使用△符号强调，独立成段
3.台词对话独立成段，标明说话人，说话人和台词使用字体加粗强调
4.人物首次出场标注年龄和身份、外貌，使用△符号强调
5.环境 、人物 、 动作不要分层描写，环境描写融入动作段落，不单独分层。动作描写融入叙事，不单独分层
6.需要展现镜头运动
7.转场明确提示下一场戏的衔接点
8.包含详细的镜头语言、镜头调度、表演、光影设计等，可用于AI生视频，两版剧本剧情内容一致，仅有无镜头语言区别。要求台词丰富剧情上下文连贯，叙事连贯，分批次输出。

内容：${item.input || ''}`;

            // 生成标准剧本
            console.log('开始生成标准剧本...');
            const standardResult = await aiService.sendMessage(provider, standardScriptPrompt);

            // 在第一个API调用后检查是否被停止
            if (!get().isGenerating) {
              console.log('在标准剧本生成后检测到停止信号');
              get().updateBatchItem(item.id, {
                status: 'pending', // 重置为待生成状态
              });
              break;
            }

            let standardScript = '';
            if (standardResult.success && standardResult.data) {
              standardScript = standardResult.data;
            }

            // 生成导演剧本
            console.log('开始生成导演剧本...');
            const directorResult = await aiService.sendMessage(provider, directorScriptPrompt);

            // 在第二个API调用后检查是否被停止
            if (!get().isGenerating) {
              console.log('在导演剧本生成后检测到停止信号');
              // 如果标准剧本已经生成成功，保存部分结果
              if (standardResult.success && standardScript) {
                get().updateBatchItem(item.id, {
                  status: 'completed',
                  result: standardScript,
                  scriptResults: {
                    standard: standardScript,
                    director: '',
                    combined: `=== 标准版剧本 ===\n\n${standardScript}\n\n=== 导演版剧本 ===\n\n(生成被中断)`,
                  },
                  endTime: Date.now(),
                });
              } else {
                get().updateBatchItem(item.id, {
                  status: 'pending', // 重置为待生成状态
                });
              }
              break;
            }

            let directorScript = '';
            if (directorResult.success && directorResult.data) {
              directorScript = directorResult.data;
            }

            // 生成剧本合集（包含两个版本）
            const combinedScript = `=== 标准版剧本 ===\n\n${standardScript}\n\n=== 导演版剧本 ===\n\n${directorScript}`;

            // 检查是否至少有一个版本生成成功
            if (standardResult.success || directorResult.success) {
              get().updateBatchItem(item.id, {
                status: 'completed',
                result: standardScript || directorScript, // 保持向后兼容
                scriptResults: {
                  standard: standardScript,
                  director: directorScript,
                  combined: combinedScript,
                },
                endTime: Date.now(),
              });
            } else {
              get().updateBatchItem(item.id, {
                status: 'failed',
                error: standardResult.error || directorResult.error || '生成失败',
                endTime: Date.now(),
              });
            }
          } catch (error) {
            // 检查是否是因为停止而导致的错误
            if (!get().isGenerating) {
              get().updateBatchItem(item.id, {
                status: 'pending', // 重置为待生成状态
              });
              break;
            }

            get().updateBatchItem(item.id, {
              status: 'failed',
              error: error instanceof Error ? error.message : '生成失败',
              endTime: Date.now(),
            });
          }

          completed++;
          set((state) => ({
            progress: { current: completed, total: state.progress.total }
          }));

          // 在延迟期间也检查是否被停止
          for (let i = 0; i < 10; i++) {
            if (!get().isGenerating) {
              console.log('在延迟期间检测到停止信号');
              break;
            }
            await new Promise(resolve => setTimeout(resolve, 100)); // 分成10个100ms的小延迟
          }

          // 最终检查是否被停止
          if (!get().isGenerating) {
            break;
          }
        }

        console.log('批量生成循环结束，设置 isGenerating 为 false');
        set({
          isGenerating: false,
          abortController: null
        });
      },

      generateSingle: async (id) => {
        const { items } = get();
        const item = items.find(item => item.id === id);
        
        if (!item) {
          set({ error: '项目不存在' });
          return;
        }

        const geminiService = getGeminiService();
        if (!geminiService) {
          set({ error: 'Gemini 服务未初始化，请先配置 API Key' });
          return;
        }

        // 更新项目状态
        get().updateBatchItem(id, { 
          status: 'generating',
          startTime: Date.now()
        });

        try {
          // 构建完整的提示词
          let finalPrompt = item.prompt;
          if (item.variables) {
            Object.entries(item.variables).forEach(([key, value]) => {
              finalPrompt = finalPrompt.replace(new RegExp(`{${key}}`, 'g'), value);
            });
          }

          // 调用 API 生成内容
          const result = await geminiService.sendMessage(finalPrompt);

          if (result.success && result.data) {
            get().updateBatchItem(id, {
              status: 'completed',
              result: result.data,
              endTime: Date.now(),
            });
          } else {
            get().updateBatchItem(id, {
              status: 'failed',
              error: result.error || '生成失败',
              endTime: Date.now(),
            });
          }
        } catch (error) {
          get().updateBatchItem(id, {
            status: 'failed',
            error: error instanceof Error ? error.message : '生成失败',
            endTime: Date.now(),
          });
        }
      },

      stopGeneration: () => {
        const { abortController } = get();

        // 中止正在进行的请求
        if (abortController) {
          console.log('中止正在进行的API请求');
          abortController.abort();
        }

        console.log('停止批量生成');
        set({
          isGenerating: false,
          abortController: null
        });
      },

      // Batch text processing
      addBatchTexts: (prompt, texts, clearExisting = false) => {
        let segments: string[] = [];

        // 检测是否包含"第*集"格式
        const episodePattern = /第(?:[一二三四五六七八九十百千万]+|\d+)集[：:]/g;
        const episodeMatches = texts.match(episodePattern);

        if (episodeMatches && episodeMatches.length > 0) {
          // 按"第*集"分割
          const parts = texts.split(episodePattern);

          // 第一部分通常是空的或者是前言，跳过
          for (let i = 1; i < parts.length; i++) {
            const episodeTitle = episodeMatches[i - 1] || '';
            const content = parts[i].trim();
            if (content) {
              segments.push(`${episodeTitle}\n${content}`);
            }
          }

          console.log(`检测到"第*集"格式，分割为 ${segments.length} 个片段`);
        } else {
          // 按行分割（原有逻辑）
          segments = texts.split('\n').filter(line => line.trim());
          console.log(`按行分割为 ${segments.length} 个片段`);
        }

        const newItems = segments.map(segment => ({
          input: segment.trim(),
          prompt: prompt || '',
          status: 'pending' as const,
          result: '',
          error: undefined,
        }));

        // 根据参数决定是否清除现有项目
        if (clearExisting) {
          set({ items: [] });
        }
        newItems.forEach(item => get().addBatchItem(item));
      },

      // Export
      exportResults: (format) => {
        const { items } = get();
        const completedItems = items.filter(item => item.status === 'completed');
        
        if (completedItems.length === 0) {
          set({ error: '没有已完成的结果可导出' });
          return;
        }

        let content = '';
        let filename = '';
        let mimeType = '';

        switch (format) {
          case 'json':
            content = JSON.stringify(completedItems, null, 2);
            filename = `batch-results-${new Date().toISOString().split('T')[0]}.json`;
            mimeType = 'application/json';
            break;
          
          case 'csv':
            const headers = ['ID', '提示词', '结果', '创建时间', '完成时间'];
            const rows = completedItems.map(item => [
              item.id,
              item.prompt.replace(/"/g, '""'),
              item.result?.replace(/"/g, '""') || '',
              new Date(item.createdAt).toLocaleString(),
              item.endTime ? new Date(item.endTime).toLocaleString() : '',
            ]);
            content = [headers, ...rows].map(row => 
              row.map(cell => `"${cell}"`).join(',')
            ).join('\n');
            filename = `batch-results-${new Date().toISOString().split('T')[0]}.csv`;
            mimeType = 'text/csv';
            break;
          
          case 'txt':
            content = completedItems.map((item, index) => 
              `=== 结果 ${index + 1} ===\n提示词: ${item.prompt}\n\n结果:\n${item.result}\n\n`
            ).join('');
            filename = `batch-results-${new Date().toISOString().split('T')[0]}.txt`;
            mimeType = 'text/plain';
            break;
        }

        // 下载文件
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
      },

      exportBatchResults: async (format, scriptType) => {
        const { items } = get();
        const completedItems = items.filter(item => item.status === 'completed');

        if (completedItems.length === 0) {
          console.warn('没有已完成的结果可导出');
          return;
        }

        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

        // 根据剧本类型获取对应的内容
        const getScriptContent = (item: any) => {
          if (!item.scriptResults) {
            return item.result || ''; // 向后兼容
          }

          switch (scriptType) {
            case 'standard':
              return item.scriptResults.standard || '';
            case 'director':
              return item.scriptResults.director || '';
            case 'combined':
              return item.scriptResults.combined || '';
            default:
              return item.result || '';
          }
        };

        // 生成文件名
        const scriptTypeNames = {
          standard: '标准剧本',
          director: '导演剧本',
          combined: '剧本合集'
        };
        const typeName = scriptTypeNames[scriptType];

        if (format === 'txt') {
          // TXT格式导出
          const content = completedItems.map((item, index) => {
            const episodeTitle = item.input?.match(/第[一二三四五六七八九十百千万\d]+集/)?.[0] || `第${index + 1}集`;
            const scriptContent = getScriptContent(item);
            return `=== ${episodeTitle} - ${typeName} ===\n\n${scriptContent}\n\n`;
          }).join('');

          const blob = new Blob([content], { type: 'text/plain' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${typeName}-${timestamp}.txt`;
          a.click();
          URL.revokeObjectURL(url);
        } else if (format === 'docx') {
          // DOCX格式导出
          const children: Paragraph[] = [];

          // 添加标题
          children.push(new Paragraph({
            children: [
              new TextRun({
                text: `${typeName} - 批量生成结果`,
                bold: true,
                size: 32,
              }),
            ],
          }));

          children.push(new Paragraph({
            children: [
              new TextRun({
                text: `导出时间：${new Date().toLocaleString()}`,
                size: 20,
                color: "666666",
              }),
            ],
          }));

          children.push(new Paragraph({ text: "" })); // 空行

          // 添加每个结果
          completedItems.forEach((item, index) => {
            const episodeTitle = item.input?.match(/第[一二三四五六七八九十百千万\d]+集/)?.[0] || `第${index + 1}集`;
            const scriptContent = getScriptContent(item);

            children.push(new Paragraph({
              children: [
                new TextRun({
                  text: `${episodeTitle}`,
                  bold: true,
                  size: 28,
                }),
              ],
            }));

            children.push(new Paragraph({ text: "" })); // 空行

            // 将剧本内容按行分割并添加
            const lines = scriptContent.split('\n');
            lines.forEach((line: string) => {
              children.push(new Paragraph({
                children: [
                  new TextRun({
                    text: line,
                    size: 22,
                  }),
                ],
              }));
            });

            children.push(new Paragraph({ text: "" })); // 空行分隔
            children.push(new Paragraph({ text: "=" .repeat(50) })); // 分隔线
            children.push(new Paragraph({ text: "" })); // 空行
          });

          const doc = new Document({
            sections: [{
              properties: {},
              children: children,
            }],
          });

          // 生成DOCX文件
          const buffer = await Packer.toBuffer(doc);
          const blob = new Blob([buffer], {
            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${typeName}-${timestamp}.docx`;
          a.click();
          URL.revokeObjectURL(url);
        }
      },

      // State management
      setLoading: (loading) => {
        set({ loading });
      },

      setError: (error) => {
        set({ error });
      },

      setProgress: (progress) => {
        set({ progress });
      },

      pauseGeneration: () => {
        set({ isPaused: true });
      },

      resumeGeneration: async () => {
        set({ isPaused: false });
      },
    }),
    {
      name: 'batch-storage',
      partialize: (state) => ({
        items: state.items,
        templates: state.templates,
      }),
    }
  )
);
