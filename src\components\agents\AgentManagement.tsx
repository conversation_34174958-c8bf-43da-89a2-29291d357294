'use client';

import React, { useState, useEffect } from 'react';
import { createStyles } from 'antd-style';
import {
  Table,
  Button,
  Switch,
  Tag,
  Space,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Divider,
  Popconfirm,
  App,
  Typography,
  Upload,
  Radio
} from 'antd';
import {
  Plus,
  Edit,
  Trash2,
  DollarSign,
  Clock,
  Hash,
  Upload as UploadIcon,
  Gift
} from 'lucide-react';
import { Agent, AgentCategory, AGENT_CATEGORIES, AgentPricingPlan, AgentType } from '@/types/agent';
import { agentsApi } from '@/lib/api';
import RedemptionCodeModal from './RedemptionCodeModal';
import { useAgentStore } from '@/store/agent';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    padding: 0;
    width: 100%;
    overflow: hidden;

    @media (max-width: 768px) {
      overflow: auto;
      -webkit-overflow-scrolling: touch;
    }
  `,

  header: css`
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
      margin-bottom: 16px;
    }
  `,

  tableContainer: css`
    width: 100%;
    overflow-x: auto;

    .ant-table-wrapper {
      width: 100%;

      .ant-table {
        min-width: 1180px;
      }

      .ant-table-container {
        overflow-x: auto;
      }

      .ant-table-thead > tr > th {
        white-space: nowrap;
      }

      .ant-table-tbody > tr > td {
        white-space: nowrap;
      }
    }

    @media (max-width: 1400px) {
      .ant-table {
        font-size: 13px;
      }
    }

    @media (max-width: 1200px) {
      .ant-table {
        font-size: 12px;
      }
    }

    @media (max-width: 768px) {
      overflow: auto;
      -webkit-overflow-scrolling: touch;

      .ant-table {
        min-width: 800px;
        font-size: 11px;
      }

      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 4px;
      }

      /* 移动端智能体名称列宽度调整 */
      .ant-table-thead > tr > th:first-child,
      .ant-table-tbody > tr > td:first-child {
        min-width: 60px !important;
        width: 60px !important;
      }
    }
  `,
  
  agentCard: css`
    margin-bottom: 16px;
    
    .ant-card-head {
      border-bottom: 1px solid ${token.colorBorderSecondary};
    }
  `,
  
  pricingSection: css`
    margin-top: 16px;
    
    .pricing-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }
  `,
  
  pricingCard: css`
    border: 1px solid ${token.colorBorderSecondary};
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    
    .pricing-type {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
    }
    
    .pricing-details {
      color: ${token.colorTextSecondary};
      font-size: 14px;
    }
    
    .pricing-actions {
      margin-top: 8px;
      display: flex;
      gap: 8px;
    }
  `,
  
  modalForm: css`
    .ant-form-item {
      margin-bottom: 16px;
    }
  `,
}));

interface AgentManagementProps {}

const AgentManagement: React.FC<AgentManagementProps> = () => {
  const { styles } = useStyles();
  const { message } = App.useApp();
  const { fetchAgents } = useAgentStore();

  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showPricingModal, setShowPricingModal] = useState(false);
  const [showRedemptionModal, setShowRedemptionModal] = useState(false);
  const [editingAgent, setEditingAgent] = useState<Agent | null>(null);
  const [selectedAgentId, setSelectedAgentId] = useState<string>('');
  const [pricingPlans, setPricingPlans] = useState<AgentPricingPlan[]>([]);
  const [presetPrompts, setPresetPrompts] = useState<string[]>([]);
  const [newPrompt, setNewPrompt] = useState('');
  const [avatarUrl, setAvatarUrl] = useState<string>('');
  const [agentType, setAgentType] = useState<AgentType>('coze');

  const [form] = Form.useForm();
  const [pricingForm] = Form.useForm();

  // 加载智能体列表
  const loadAgents = async () => {
    setLoading(true);
    try {
      const response = await agentsApi.getAll();
      if (response.success && response.data) {
        setAgents(response.data);
      } else {
        message.error(response.error || '加载智能体失败');
      }
    } catch (error) {
      message.error('加载智能体失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载定价方案
  const loadPricingPlans = async (agentId: string) => {
    try {
      const response = await fetch(`/api/agent-pricing?agentId=${agentId}`);
      const result = await response.json();
      if (result.success) {
        setPricingPlans(result.data || []);
      }
    } catch (error) {
      console.error('加载定价方案失败:', error);
    }
  };



  useEffect(() => {
    loadAgents();
  }, []);

  // 处理智能体启用/禁用
  const handleToggleEnabled = async (agent: Agent, enabled: boolean) => {
    try {
      const response = await agentsApi.update(agent.id, { enabled });
      if (response.success) {
        message.success(enabled ? '智能体已启用' : '智能体已禁用');
        loadAgents();
        // 同时更新全局智能体store
        fetchAgents();
      } else {
        message.error(response.error || '操作失败');
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 处理添加智能体
  const handleAddAgent = () => {
    setEditingAgent(null);
    form.resetFields();
    setPresetPrompts([]);
    setNewPrompt('');
    setAvatarUrl('');
    setShowAddModal(true);
  };

  // 处理编辑智能体
  const handleEditAgent = (agent: Agent) => {
    setEditingAgent(agent);
    setAgentType(agent.type || 'coze');
    form.setFieldsValue({
      name: agent.name,
      description: agent.description,
      category: agent.category,
      usageInstructions: agent.usageInstructions,
      purchaseLink: agent.purchaseLink,
      // Coze智能体字段
      cozeApiKey: agent.cozeConfig?.apiKey,
      cozeBotId: agent.cozeConfig?.botId,
      cozeUserId: agent.cozeConfig?.userId,
      // 内置智能体字段
      secretCode: agent.builtinConfig?.secretCode,
      systemPrompt: agent.builtinConfig?.systemPrompt,
      trialUsageCount: agent.trialUsageCount || 3,
    });
    setPresetPrompts(agent.presetPrompts || []);
    setAvatarUrl(agent.avatar || '');
    setShowAddModal(true);
  };

  // 处理删除智能体
  const handleDeleteAgent = async (agentId: string) => {
    try {
      const response = await agentsApi.delete(agentId);
      if (response.success) {
        message.success('智能体删除成功');
        loadAgents();
        // 同时更新全局智能体store
        fetchAgents();
      } else {
        message.error(response.error || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 处理保存智能体
  const handleSaveAgent = async (values: any) => {
    try {
      // 处理图标路径：如果没有上传图标，使用默认图标
      let finalAvatarUrl = avatarUrl;
      if (!avatarUrl) {
        finalAvatarUrl = '/favicon.png'; // 默认图标
      }

      const agentData = {
        name: values.name,
        description: values.description,
        category: values.category,
        type: agentType,
        avatar: finalAvatarUrl,
        usageInstructions: values.usageInstructions,
        presetPrompts: presetPrompts.filter(prompt => prompt.trim() !== ''),
        purchaseLink: values.purchaseLink,
        // Coze智能体字段
        cozeApiKey: agentType === 'coze' ? values.cozeApiKey || '' : undefined,
        cozeBotId: agentType === 'coze' ? values.cozeBotId || '' : undefined,
        cozeUserId: agentType === 'coze' ? values.cozeUserId || '' : undefined,
        // 内置智能体字段
        secretCode: agentType === 'builtin' ? values.secretCode || '' : undefined,
        systemPrompt: agentType === 'builtin' ? values.systemPrompt || '' : undefined,
        trialUsageCount: values.trialUsageCount || 3,
      };

      let response;
      if (editingAgent) {
        response = await agentsApi.update(editingAgent.id, agentData);
      } else {
        response = await agentsApi.create(agentData);
      }

      if (response.success) {
        // 如果是新创建的智能体且上传了图标，需要重命名图标文件（用真实ID命名）
        if (!editingAgent && avatarUrl && avatarUrl.includes('temp_')) {
          try {
            // 获取新创建的智能体ID
            const newAgentId = (response as any).data?.id;
            if (newAgentId) {
              // 提取临时ID
              const tempId = avatarUrl.split('/').pop()?.split('.')[0];
              if (tempId) {
                // 调用重命名API
                const renameResponse = await fetch('/api/rename-agent-avatar', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    tempId: tempId,
                    newAgentId: newAgentId
                  })
                });

                const renameResult = await renameResponse.json();
                if (renameResult.success) {
                  console.log('图标文件重命名成功:', renameResult.url);
                } else {
                  console.warn('图标文件重命名失败:', renameResult.error);
                }
              }
            }
          } catch (renameError) {
            console.warn('重命名图标文件失败:', renameError);
            // 不影响主流程，只是记录警告
          }
        }

        message.success(editingAgent ? '智能体更新成功' : '智能体创建成功');
        // 成功后清空状态并关闭模态框
        form.resetFields();
        setPresetPrompts([]);
        setNewPrompt('');
        setAvatarUrl('');
        setEditingAgent(null);
        setShowAddModal(false);
        loadAgents();
        // 同时更新全局智能体store，确保会话中的预设提示词能及时更新
        fetchAgents();
      } else {
        message.error(response.error || '保存失败');
      }
    } catch (error) {
      message.error('保存失败');
    }
  };

  // 处理定价管理
  const handleManagePricing = (agentId: string) => {
    setSelectedAgentId(agentId);
    loadPricingPlans(agentId);
    setShowPricingModal(true);
  };

  // 处理兑换码管理
  const handleManageRedemptionCodes = (agentId: string) => {
    setSelectedAgentId(agentId);
    setShowRedemptionModal(true);
  };

  // 处理添加定价方案
  const handleAddPricingPlan = async (values: any) => {
    try {
      const response = await fetch('/api/agent-pricing', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agentId: selectedAgentId,
          ...values,
        }),
      });

      const result = await response.json();
      if (result.success) {
        message.success('定价方案创建成功');
        pricingForm.resetFields();
        loadPricingPlans(selectedAgentId);
      } else {
        message.error(result.error || '创建失败');
      }
    } catch (error) {
      message.error('创建失败');
    }
  };

  // 处理删除定价方案
  const handleDeletePricingPlan = async (planId: string) => {
    try {
      const response = await fetch(`/api/agent-pricing?id=${planId}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      if (result.success) {
        message.success('定价方案删除成功');
        loadPricingPlans(selectedAgentId);
      } else {
        message.error(result.error || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 添加预设提示词
  const addPresetPrompt = () => {
    if (newPrompt.trim() && !presetPrompts.includes(newPrompt.trim())) {
      setPresetPrompts([...presetPrompts, newPrompt.trim()]);
      setNewPrompt('');
    }
  };

  // 删除预设提示词
  const removePresetPrompt = (index: number) => {
    setPresetPrompts(presetPrompts.filter((_, i) => i !== index));
  };

  // 处理图标上传
  const handleAvatarChange = (info: any) => {
    if (info.file.status === 'done') {
      // 设置上传成功后的URL
      const url = info.file.response?.url;
      if (url) {
        setAvatarUrl(url);
      }
    } else if (info.file.status === 'error') {
      message.error('图标上传失败');
    }
  };

  // 自定义上传函数
  const customUpload = async ({ file, onSuccess, onError }: any) => {
    try {
      // 如果是编辑模式且有智能体ID，使用该ID；否则生成临时ID
      const uploadAgentId = editingAgent?.id || `temp_${Date.now()}`;

      // 创建FormData
      const formData = new FormData();
      formData.append('file', file);
      formData.append('agentId', uploadAgentId);

      // 上传文件
      const response = await fetch('/api/upload-agent-avatar', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        // 设置avatarUrl为服务器返回的路径
        setAvatarUrl(result.url);
        onSuccess({ url: result.url });
        message.success('图标上传成功');
      } else {
        throw new Error(result.error || '上传失败');
      }
    } catch (error) {
      console.error('上传失败:', error);
      message.error('图标上传失败');
      onError(error);
    }
  };

  // 处理模态框关闭（不清空编辑状态）
  const handleModalClose = () => {
    setShowAddModal(false);
  };

  // 处理取消操作（清空所有状态）
  const handleCancel = () => {
    form.resetFields();
    setPresetPrompts([]);
    setNewPrompt('');
    setAvatarUrl('');
    setAgentType('coze');
    setEditingAgent(null);
    setShowAddModal(false);
  };

  const columns = [
    {
      title: '智能体名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      fixed: 'left' as const,
      render: (text: string, record: Agent) => (
        <Space>
          <img src={record.avatar || "/favicon.png"} alt="" style={{ width: 20, height: 20, borderRadius: '4px' }} />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 280,
      ellipsis: true,
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category: AgentCategory) => {
        const categoryInfo = AGENT_CATEGORIES.find(c => c.key === category);
        return <Tag color="blue">{categoryInfo?.label || category}</Tag>;
      },
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: AgentType) => (
        <Tag color={type === 'coze' ? 'green' : 'orange'}>
          {type === 'coze' ? 'Coze智能体' : '内置智能体'}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      width: 80,
      render: (enabled: boolean, record: Agent) => (
        <Switch
          checked={enabled}
          onChange={(checked) => handleToggleEnabled(record, checked)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
    },
    {
      title: '使用次数',
      dataIndex: 'usageCount',
      key: 'usageCount',
      width: 90,
      render: (count: number) => count || 0,
    },
    {
      title: '试用次数',
      dataIndex: 'trialUsageCount',
      key: 'trialUsageCount',
      width: 90,
      render: (count: number) => (
        <Tag color="blue">{count || 3} 次</Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 240,
      fixed: 'right' as const,
      render: (_: any, record: Agent) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<Edit size={14} />}
            onClick={() => handleEditAgent(record)}
          >
            编辑
          </Button>
          <Button
            type="text"
            size="small"
            icon={<DollarSign size={14} />}
            onClick={() => handleManagePricing(record.id)}
          >
            定价
          </Button>
          <Button
            type="text"
            size="small"
            icon={<Gift size={14} />}
            onClick={() => handleManageRedemptionCodes(record.id)}
          >
            兑换码
          </Button>
          <Popconfirm
            title="确定要删除这个智能体吗？"
            onConfirm={() => handleDeleteAgent(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<Trash2 size={14} />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div>
          <Title level={3}>智能体管理</Title>
          <Text type="secondary">管理智能体和定价方案</Text>
        </div>
        <Button
          type="primary"
          icon={<Plus size={16} />}
          onClick={handleAddAgent}
        >
          添加智能体
        </Button>
      </div>

      <div className={styles.tableContainer}>
        <Table
          columns={columns}
          dataSource={agents}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1180, y: 'calc(100vh - 300px)' }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个智能体`,
          }}
        />
      </div>

      {/* 添加/编辑智能体模态框 */}
      <Modal
        title={editingAgent ? '编辑智能体' : '添加智能体'}
        open={showAddModal}
        onCancel={handleModalClose}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveAgent}
          className={styles.modalForm}
        >
          <Form.Item
            name="name"
            label="智能体名称"
            rules={[{ required: true, message: '请输入智能体名称' }]}
          >
            <Input placeholder="请输入智能体名称" />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <TextArea placeholder="请输入智能体描述" rows={3} />
          </Form.Item>

          <Form.Item name="usageInstructions" label="使用说明">
            <TextArea
              placeholder="请输入智能体使用说明（可选）"
              rows={4}
              showCount
              maxLength={5000}
            />
          </Form.Item>

          <Form.Item
            name="category"
            label="分类"
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select placeholder="请选择分类">
              {AGENT_CATEGORIES.map(category => (
                <Option key={category.key} value={category.key}>
                  {category.label} - {category.description}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="智能体类型"
            required
          >
            <Radio.Group
              value={agentType}
              onChange={(e) => setAgentType(e.target.value)}
            >
              <Radio value="coze">Coze智能体</Radio>
              <Radio value="builtin">内置智能体</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            label="智能体图标"
            tooltip="上传智能体的头像图标，支持 JPG、PNG 格式，建议尺寸 64x64 像素"
          >
            <Upload
              name="avatar"
              listType="picture-card"
              className="avatar-uploader"
              showUploadList={false}
              customRequest={customUpload}
              onChange={handleAvatarChange}
              accept="image/*"
            >
              {avatarUrl ? (
                <img src={avatarUrl} alt="avatar" style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
              ) : (
                <div>
                  <UploadIcon size={16} />
                  <div style={{ marginTop: 8 }}>上传图标</div>
                </div>
              )}
            </Upload>
          </Form.Item>

          <Form.Item
            label="预设提示词"
            tooltip="设置一些常用的提示词，用户可以快速点击发送，最多设置8个"
          >
            <div style={{ marginBottom: 8 }}>
              <Space.Compact style={{ width: '100%' }}>
                <Input
                  placeholder="输入预设提示词"
                  value={newPrompt}
                  onChange={(e) => setNewPrompt(e.target.value)}
                  onPressEnter={addPresetPrompt}
                  maxLength={50}
                />
                <Button
                  type="primary"
                  onClick={addPresetPrompt}
                  disabled={!newPrompt.trim() || presetPrompts.length >= 8}
                >
                  添加
                </Button>
              </Space.Compact>
            </div>
            <div style={{ minHeight: 32 }}>
              {presetPrompts.map((prompt, index) => (
                <Tag
                  key={index}
                  closable
                  onClose={() => removePresetPrompt(index)}
                  style={{ marginBottom: 4 }}
                >
                  {prompt}
                </Tag>
              ))}
              {presetPrompts.length === 0 && (
                <span style={{ color: '#999', fontSize: '12px' }}>
                  暂无预设提示词，添加后将在对话界面显示
                </span>
              )}
            </div>
          </Form.Item>

          {/* Coze智能体字段 */}
          {agentType === 'coze' && (
            <>
              <Divider>Coze 配置</Divider>
              <Form.Item
                name="cozeApiKey"
                label="Coze API Key"
                rules={[{ required: true, message: '请输入 Coze API Key' }]}
              >
                <Input.Password placeholder="请输入 Coze API Key" />
              </Form.Item>

              <Form.Item
                name="cozeBotId"
                label="Bot ID"
                rules={[{ required: true, message: '请输入 Bot ID' }]}
              >
                <Input placeholder="请输入 Bot ID" />
              </Form.Item>

              <Form.Item
                name="cozeUserId"
                label="User ID"
                rules={[{ required: true, message: '请输入 User ID' }]}
              >
                <Input placeholder="请输入 User ID" />
              </Form.Item>
            </>
          )}

          {/* 内置智能体字段 */}
          {agentType === 'builtin' && (
            <>
              <Divider>内置智能体配置</Divider>
              <Form.Item
                name="secretCode"
                label="暗号"
                rules={[{ required: true, message: '请输入暗号' }]}
              >
                <Input placeholder="请输入智能体暗号" />
              </Form.Item>

              <Form.Item
                name="systemPrompt"
                label="提示词内容"
                rules={[{ required: true, message: '请输入提示词内容' }]}
              >
                <TextArea
                  placeholder="请输入系统提示词内容"
                  rows={6}
                  showCount
                />
              </Form.Item>
            </>
          )}

          <Form.Item
            name="trialUsageCount"
            label="试用次数"
            rules={[
              { required: true, message: '请输入试用次数' },
              { type: 'number', min: 0, max: 100, message: '试用次数必须在0-100之间' }
            ]}
            tooltip="用户在没有付费订阅的情况下可以免费试用该智能体的次数"
          >
            <InputNumber
              placeholder="请输入试用次数"
              min={0}
              max={100}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="purchaseLink"
            label="购买链接"
            rules={[
              { type: 'url', message: '请输入有效的URL地址' }
            ]}
            tooltip="用户点击购买或升级按钮时将跳转到此链接"
          >
            <Input
              placeholder="请输入购买链接（可选）"
              prefix="🔗"
            />
          </Form.Item>



          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingAgent ? '更新' : '创建'}
              </Button>
              <Button onClick={handleCancel}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 定价管理模态框 */}
      <Modal
        title="定价方案管理"
        open={showPricingModal}
        onCancel={() => setShowPricingModal(false)}
        footer={null}
        width={800}
      >
        <div className={styles.pricingSection}>
          <div className="pricing-header">
            <Title level={4}>现有定价方案</Title>
          </div>

          {pricingPlans.map((plan) => (
            <div key={plan.id} className={styles.pricingCard}>
              <div className="pricing-type">
                {plan.type === 'per_usage' ? (
                  <>
                    <Hash size={16} />
                    <Text strong>按次收费</Text>
                    <Tag color="green">{plan.name}</Tag>
                  </>
                ) : (
                  <>
                    <Clock size={16} />
                    <Text strong>按时计费</Text>
                    <Tag color="blue">{plan.name}</Tag>
                  </>
                )}
              </div>

              <div className="pricing-details">
                {plan.type === 'per_usage' ? (
                  <Text>
                    {plan.usageCount} 次使用，每次 ¥{plan.pricePerUsage}
                  </Text>
                ) : (
                  <Text>
                    {plan.durationDays} 天，总价 ¥{plan.pricePerPeriod}
                  </Text>
                )}
                {plan.description && (
                  <div style={{ marginTop: 4 }}>
                    <Text type="secondary">{plan.description}</Text>
                  </div>
                )}
              </div>

              <div className="pricing-actions">
                <Popconfirm
                  title="确定要删除这个定价方案吗？"
                  onConfirm={() => handleDeletePricingPlan(plan.id)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button size="small" danger icon={<Trash2 size={14} />}>
                    删除
                  </Button>
                </Popconfirm>
              </div>
            </div>
          ))}

          <Divider>添加新定价方案</Divider>

          <Form
            form={pricingForm}
            layout="vertical"
            onFinish={handleAddPricingPlan}
          >
            <Form.Item
              name="name"
              label="方案名称"
              rules={[{ required: true, message: '请输入方案名称' }]}
            >
              <Input placeholder="例如：基础套餐、专业版等" />
            </Form.Item>

            <Form.Item
              name="type"
              label="计费类型"
              rules={[{ required: true, message: '请选择计费类型' }]}
            >
              <Select placeholder="请选择计费类型" onChange={() => pricingForm.resetFields(['usageCount', 'pricePerUsage', 'durationDays', 'pricePerPeriod'])}>
                <Option value="per_usage">按次收费</Option>
                <Option value="time_based">按时计费</Option>
              </Select>
            </Form.Item>

            <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}>
              {({ getFieldValue }) => {
                const type = getFieldValue('type');

                if (type === 'per_usage') {
                  return (
                    <>
                      <Form.Item
                        name="usageCount"
                        label="使用次数"
                        rules={[{ required: true, message: '请输入使用次数' }]}
                      >
                        <InputNumber
                          min={1}
                          placeholder="可使用次数"
                          style={{ width: '100%' }}
                          addonAfter="次"
                        />
                      </Form.Item>

                      <Form.Item
                        name="pricePerUsage"
                        label="单次价格"
                        rules={[{ required: true, message: '请输入单次价格' }]}
                      >
                        <InputNumber
                          min={0}
                          step={0.01}
                          placeholder="每次使用的价格"
                          style={{ width: '100%' }}
                          addonBefore="¥"
                        />
                      </Form.Item>
                    </>
                  );
                }

                if (type === 'time_based') {
                  return (
                    <>
                      <Form.Item
                        name="durationDays"
                        label="有效期"
                        rules={[{ required: true, message: '请输入有效期' }]}
                      >
                        <InputNumber
                          min={1}
                          placeholder="有效天数"
                          style={{ width: '100%' }}
                          addonAfter="天"
                        />
                      </Form.Item>

                      <Form.Item
                        name="pricePerPeriod"
                        label="总价格"
                        rules={[{ required: true, message: '请输入总价格' }]}
                      >
                        <InputNumber
                          min={0}
                          step={0.01}
                          placeholder="整个期间的价格"
                          style={{ width: '100%' }}
                          addonBefore="¥"
                        />
                      </Form.Item>
                    </>
                  );
                }

                return null;
              }}
            </Form.Item>

            <Form.Item name="description" label="方案描述">
              <TextArea placeholder="请输入方案描述" rows={2} />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  添加方案
                </Button>
                <Button onClick={() => pricingForm.resetFields()}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </div>
      </Modal>

      {/* 兑换码管理弹窗 */}
      <RedemptionCodeModal
        visible={showRedemptionModal}
        onCancel={() => setShowRedemptionModal(false)}
        agentId={selectedAgentId}
        agentName={agents.find(a => a.id === selectedAgentId)?.name}
      />
    </div>
  );
};

export default AgentManagement;
