'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createStyles } from 'antd-style';
import { Form, Input, Button, App, Card } from 'antd';
import { Lock, Eye, EyeOff } from 'lucide-react';

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: ${token.colorBgLayout};
    padding: 20px;
  `,
  formContainer: css`
    width: 100%;
    max-width: 400px;
  `,
  header: css`
    text-align: center;
    margin-bottom: 32px;

    .title {
      font-size: 32px;
      font-weight: bold;
      color: ${token.colorPrimary};
      margin: 0 0 8px 0;
    }

    .subtitle {
      color: ${token.colorTextSecondary};
      margin: 0;
    }
  `,
  card: css`
    box-shadow: ${token.boxShadowTertiary};
    border-radius: ${token.borderRadiusLG}px;
  `,
  cardHeader: css`
    text-align: center;
    margin-bottom: 24px;

    .title {
      font-size: 24px;
      font-weight: 600;
      color: ${token.colorText};
      margin: 0 0 8px 0;
    }

    .description {
      color: ${token.colorTextSecondary};
      margin: 0;
    }
  `,
  form: css`
    .ant-form-item {
      margin-bottom: 20px;
    }

    .ant-input-affix-wrapper {
      padding: 12px 16px;
      border-radius: ${token.borderRadius}px;

      .ant-input {
        font-size: 16px;
      }
    }

    .ant-btn {
      height: 48px;
      font-size: 16px;
      font-weight: 500;
      border-radius: ${token.borderRadius}px;
      width: 100%;
    }
  `,
  switchMode: css`
    text-align: center;
    margin-top: 24px;

    .link {
      color: ${token.colorPrimary};
      cursor: pointer;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  `,
}));

interface ResetPasswordFormData {
  password: string;
  confirmPassword: string;
}

function ResetPasswordContent() {
  const { styles } = useStyles();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [validating, setValidating] = useState(true);
  const [tokenValid, setTokenValid] = useState(false);
  const [userInfo, setUserInfo] = useState<{ username: string; email: string } | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const token = searchParams.get('token');

  // 验证token有效性
  useEffect(() => {
    const validateToken = async () => {
      if (!token) {
        message.error('重置链接无效');
        router.push('/auth');
        return;
      }

      try {
        const response = await fetch(`/api/auth/reset-password?token=${token}`);
        const result = await response.json();

        if (result.success) {
          setTokenValid(true);
          setUserInfo(result.data);
        } else {
          message.error(result.error);
          router.push('/auth');
        }
      } catch (error) {
        message.error('验证重置链接失败');
        router.push('/auth');
      } finally {
        setValidating(false);
      }
    };

    validateToken();
  }, [token, router]);

  const handleSubmit = async (values: ResetPasswordFormData) => {
    if (!token) {
      message.error('重置链接无效');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          password: values.password,
          confirmPassword: values.confirmPassword,
        }),
      });

      const result = await response.json();

      if (result.success) {
        message.success('密码重置成功！请使用新密码登录');
        router.push('/auth');
      } else {
        message.error(result.error);
      }
    } catch (error) {
      message.error('重置密码失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  if (validating) {
    return (
      <div className={styles.container}>
        <div className={styles.formContainer}>
          <div className={styles.header}>
            <h1 className="title">微甜 AI Studio</h1>
            <p className="subtitle">让创意无限可能</p>
          </div>
          <Card className={styles.card}>
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600">正在验证重置链接...</p>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  if (!tokenValid) {
    return null; // 会被重定向到登录页面
  }

  return (
    <div className={styles.container}>
      <div className={styles.formContainer}>
        <div className={styles.header}>
          <h1 className="title">微甜 AI Studio</h1>
          <p className="subtitle">让创意无限可能</p>
        </div>

        <Card className={styles.card}>
          <div className={styles.cardHeader}>
            <h2 className="title">重置密码</h2>
            {userInfo && (
              <p className="description">
                为账户 <strong>{userInfo.username}</strong> ({userInfo.email}) 设置新密码
              </p>
            )}
          </div>

          <Form
            form={form}
            onFinish={handleSubmit}
            layout="vertical"
            className={styles.form}
          >
            <Form.Item
              name="password"
              label="新密码"
              rules={[
                { required: true, message: '请输入新密码' },
                { min: 8, message: '密码长度至少为8位' },
                {
                  pattern: /(?=.*[a-z])/,
                  message: '密码必须包含至少一个小写字母',
                },
                {
                  pattern: /(?=.*[A-Z])/,
                  message: '密码必须包含至少一个大写字母',
                },
                {
                  pattern: /(?=.*\d)/,
                  message: '密码必须包含至少一个数字',
                },
              ]}

            >
              <Input.Password
                prefix={<Lock size={20} />}
                placeholder="请输入新密码"
                iconRender={(visible) => (visible ? <EyeOff size={16} /> : <Eye size={16} />)}
                visibilityToggle={{
                  visible: showPassword,
                  onVisibleChange: setShowPassword,
                }}
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label="确认新密码"
              dependencies={['password']}
              rules={[
                { required: true, message: '请确认新密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  },
                }),
              ]}

            >
              <Input.Password
                prefix={<Lock size={20} />}
                placeholder="请再次输入新密码"
                iconRender={(visible) => (visible ? <EyeOff size={16} /> : <Eye size={16} />)}
                visibilityToggle={{
                  visible: showConfirmPassword,
                  onVisibleChange: setShowConfirmPassword,
                }}
              />
            </Form.Item>

            <Button
              type="primary"
              htmlType="submit"
              loading={loading}

            >
              {loading ? '重置中...' : '重置密码'}
            </Button>
          </Form>

          <div className={styles.switchMode}>
            <span
              className="link"
              onClick={() => router.push('/auth')}
            >
              返回登录
            </span>
          </div>
        </Card>
      </div>
    </div>
  );
}

export default function ResetPasswordPage() {
  return (
    <App>
      <Suspense fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">正在加载...</p>
          </div>
        </div>
      }>
        <ResetPasswordContent />
      </Suspense>
    </App>
  );
}
