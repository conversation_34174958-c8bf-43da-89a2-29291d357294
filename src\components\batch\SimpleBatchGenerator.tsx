'use client';

import React, { useState, useEffect } from 'react';
import { createStyles } from 'antd-style';
import {
  Card,
  Button,
  Input,
  Progress,
  message,
  Space,
  Dropdown
} from 'antd';
import {
  Play,
  Square,
  Download,
  Pause,
  PlayCircle,
  Trash2
} from 'lucide-react';
import { useBatchStore } from '@/store/batch';
import { SimpleModelSelector } from '@/components/ModelSelector';
import { AIProvider } from '@/types/ai-provider';
import { useAIProviderConfigsStore } from '@/store/aiProviderConfigs';
import { useAuthStore } from '@/store/auth';

const { TextArea } = Input;

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 24px;
    background: ${token.colorBgLayout};
    overflow: hidden;

    @media (max-width: 768px) {
      padding: 16px;
      overflow: auto;
      -webkit-overflow-scrolling: touch;
      height: auto;
      min-height: calc(100vh - 120px);
    }
  `,

  header: css`
    text-align: center;
    margin-bottom: 24px;

    h2 {
      margin: 0 0 8px 0;
      color: ${token.colorText};
      font-size: 24px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: ${token.colorTextSecondary};
      font-size: 14px;
      line-height: 1.5;
    }

    @media (max-width: 768px) {
      margin-bottom: 16px;

      h2 {
        font-size: 20px;
      }

      p {
        font-size: 13px;
      }
    }
  `,

  promptCard: css`
    background: ${token.colorBgContainer};
    border: 1px solid ${token.colorBorderSecondary};
    border-radius: 8px;
    margin-bottom: 16px;

    .ant-card-body {
      padding: 20px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: ${token.colorText};
    }

    @media (max-width: 768px) {
      margin-bottom: 12px;

      .ant-card-body {
        padding: 16px;
      }
    }
  `,

  inputCard: css`
    background: ${token.colorBgContainer};
    border: 1px solid ${token.colorBorderSecondary};
    border-radius: 8px;
    margin-bottom: 16px;

    .ant-card-body {
      padding: 20px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: ${token.colorText};
    }

    @media (max-width: 768px) {
      margin-bottom: 12px;

      .ant-card-body {
        padding: 16px;
      }
    }
  `,

  controlPanel: css`
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 16px 20px;
    background: ${token.colorBgContainer};
    border-radius: 8px;
    border: 1px solid ${token.colorBorderSecondary};
    margin-bottom: 16px;

    @media (max-width: 768px) {
      flex-wrap: wrap;
      gap: 8px;
      padding: 12px 16px;

      .ant-btn {
        flex: 1;
        min-width: 120px;
      }
    }
  `,

  progressSection: css`
    padding: 16px 20px;
    background: ${token.colorBgContainer};
    border-radius: 8px;
    border: 1px solid ${token.colorBorderSecondary};
    margin-bottom: 16px;

    .progress-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .progress-text {
      font-weight: 500;
      color: ${token.colorText};
    }

    @media (max-width: 768px) {
      padding: 12px 16px;

      .progress-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }

    .progress-stats {
      color: ${token.colorTextSecondary};
      font-size: 14px;
    }
  `,

  resultsContainer: css`
    flex: 1;
    background: ${token.colorBgContainer};
    border: 1px solid ${token.colorBorderSecondary};
    border-radius: 8px;
    overflow: visible;

    .ant-card-body {
      padding: 0;
      height: auto;
      display: flex;
      flex-direction: column;
    }
  `,

  resultsList: css`
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    
    .result-item {
      margin-bottom: 16px;
      padding: 16px;
      background: ${token.colorFillAlter};
      border-radius: 8px;
      border: 1px solid ${token.colorBorder};
      
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        .result-title {
          font-weight: 500;
          color: ${token.colorText};
        }
        
        .result-status {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
          
          &.pending {
            color: ${token.colorTextSecondary};
            background: ${token.colorFillSecondary};
          }
          
          &.generating {
            color: ${token.colorPrimary};
            background: ${token.colorPrimaryBg};
          }
          
          &.completed {
            color: ${token.colorSuccess};
            background: ${token.colorSuccessBg};
          }
          
          &.failed {
            color: ${token.colorError};
            background: ${token.colorErrorBg};
          }
        }
      }
      
      .result-content {
        white-space: pre-wrap;
        word-break: break-word;
        font-size: 14px;
        line-height: 1.6;
        color: ${token.colorText};
        background: ${token.colorBgContainer};
        padding: 12px;
        border-radius: 4px;
        border: 1px solid ${token.colorBorder};
        min-height: 60px;
      }
      
      .result-error {
        color: ${token.colorError};
        font-style: italic;
      }
    }
  `,
}));

const SimpleBatchGenerator: React.FC = () => {
  const { styles } = useStyles();
  const { user } = useAuthStore();
  const { userSettings, loadUserSettings, updateCurrentModel } = useAIProviderConfigsStore();

  const [batchPrompt, setBatchPrompt] = useState('');
  const [batchTexts, setBatchTexts] = useState('');
  const [selectedProvider, setSelectedProvider] = useState<AIProvider>(
    userSettings?.currentProvider || 'google'
  );
  const [selectedModel, setSelectedModel] = useState<string>(
    userSettings?.currentModel || 'gemini-2.0-flash'
  );

  // 加载用户设置
  useEffect(() => {
    if (user?.id) {
      loadUserSettings(user.id);
    }
  }, [user?.id, loadUserSettings]);

  // 当用户设置加载后，更新本地状态
  useEffect(() => {
    if (userSettings) {
      setSelectedProvider(userSettings.currentProvider || 'google');
      setSelectedModel(userSettings.currentModel || 'gemini-2.0-flash');
    }
  }, [userSettings]);

  // 使用批量生成状态管理
  const {
    items,
    isGenerating,
    progress,
    isPaused,
    generateBatch,
    stopGeneration,
    pauseGeneration,
    resumeGeneration,
    addBatchTexts,
    exportBatchResults,
    clearBatchItems,
  } = useBatchStore();

  // 处理模型选择
  const handleModelChange = (provider: AIProvider, model: string) => {
    setSelectedProvider(provider);
    setSelectedModel(model);
    // 注意：SimpleModelSelector内部已经会调用updateCurrentModel更新全局设置
    // 这里不需要额外调用，避免重复更新
  };

  // 开始生成
  const handleStartGeneration = async () => {
    if (!batchTexts.trim()) {
      message.warning('请输入批量文案内容');
      return;
    }

    // 确保全局设置与当前选择的模型一致
    if (user?.id && (
      userSettings?.currentProvider !== selectedProvider ||
      userSettings?.currentModel !== selectedModel
    )) {
      await updateCurrentModel(selectedProvider, selectedModel);
    }

    // 检查是否需要清除现有项目
    let shouldClearExisting = false;

    if (items.length === 0) {
      // 没有现有项目，直接添加
      shouldClearExisting = false;
    } else if (items.every(item => item.status === 'completed' || item.status === 'failed')) {
      // 所有项目都已完成/失败，清除现有项目开始新批次
      shouldClearExisting = true;
    } else {
      // 有pending或generating的项目，询问用户
      const hasPendingItems = items.some(item => item.status === 'pending' || item.status === 'generating');
      if (hasPendingItems) {
        const userChoice = window.confirm(
          '当前还有未完成的生成项目，是否要清除现有项目并开始新的批次？\n\n' +
          '点击"确定"：清除现有项目，开始新批次\n' +
          '点击"取消"：将新项目添加到现有列表中'
        );
        shouldClearExisting = userChoice;
      } else {
        shouldClearExisting = true;
      }
    }

    // 使用批量文本处理功能，支持"第*集"分割
    addBatchTexts(batchPrompt.trim(), batchTexts.trim(), shouldClearExisting);

    // 开始生成，传入选择的模型
    await generateBatch(selectedProvider, selectedModel);
  };

  // 暂停生成
  const handlePause = () => {
    pauseGeneration();
    message.info('已暂停生成');
  };

  // 继续生成
  const handleResume = async () => {
    message.info('继续生成...');
    await resumeGeneration();
  };

  // 停止生成
  const handleStop = () => {
    stopGeneration();
    message.info('已停止生成');
  };

  // 导出结果
  const handleExport = async (format: 'txt' | 'docx', scriptType: 'standard' | 'director' | 'combined') => {
    try {
      await exportBatchResults(format, scriptType);
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
    }
  };

  // 清除结果
  const handleClear = () => {
    if (window.confirm('确定要清除所有批量生成结果吗？')) {
      clearBatchItems();
      // 不清空输入框内容，保留用户的输入
      message.success('已清除所有结果');
    }
  };

  // 计算进度百分比
  const progressPercent = progress.total > 0 ? Math.round((progress.current / progress.total) * 100) : 0;

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '等待中';
      case 'generating': return '生成中';
      case 'completed': return '已完成';
      case 'failed': return '失败';
      default: return '未知';
    }
  };

  return (
    <div className={styles.container}>
      {/* 头部 */}
      <div className={styles.header}>
        <h2>批量文案生成</h2>
        <p>智能识别"第*集"分割文案（如：第一集、第二集、第1集等），如无此标识则按行分割。</p>
      </div>

      {/* 模型选择 */}
      <Card className={styles.promptCard}>
        <label htmlFor="modelSelector">AI 模型选择</label>
        <div style={{ opacity: isGenerating ? 0.6 : 1, pointerEvents: isGenerating ? 'none' : 'auto' }}>
          <SimpleModelSelector
            value={{ provider: selectedProvider, model: selectedModel }}
            onChange={handleModelChange}
            placeholder="选择用于批量生成的AI模型"
            size="middle"
            style={{ width: '30%' }}
          />
        </div>
      </Card>

      {/* 统一提示词 */}
      <Card className={styles.promptCard}>
        <label htmlFor="batchPrompt">统一提示词（可选）</label>
        <TextArea
          id="batchPrompt"
          value={batchPrompt}
          onChange={(e) => setBatchPrompt(e.target.value)}
          placeholder="输入统一的提示词，将会添加到每个文案前面..."
          rows={3}
          disabled={isGenerating}
        />
      </Card>

      {/* 批量文案输入 */}
      <Card className={styles.inputCard}>
        <label htmlFor="batchTexts">批量文案输入</label>
        <TextArea
          id="batchTexts"
          value={batchTexts}
          onChange={(e) => setBatchTexts(e.target.value)}
          placeholder={`请输入文案内容：

第一集：
这里是第一集的内容...

第二集：
这里是第二集的内容...

或按行输入：
文案1
文案2`}
          rows={8}
          disabled={isGenerating}
        />
      </Card>

      {/* 控制面板 */}
      <div className={styles.controlPanel}>
        {!isPaused ? (
          <Button
            type="primary"
            icon={<Play size={16} />}
            onClick={handleStartGeneration}
            disabled={isGenerating || !batchTexts.trim()}
            loading={isGenerating}
          >
            {isGenerating ? '生成中...' : '开始生成'}
          </Button>
        ) : (
          <Button
            type="primary"
            icon={<PlayCircle size={16} />}
            onClick={handleResume}
            disabled={isGenerating}
          >
            继续生成
          </Button>
        )}

        {isGenerating && !isPaused && (
          <Button
            icon={<Pause size={16} />}
            onClick={handlePause}
          >
            暂停生成
          </Button>
        )}

        <Button
          icon={<Square size={16} />}
          onClick={handleStop}
          disabled={!isGenerating}
        >
          停止
        </Button>

        <Dropdown
          menu={{
            items: [
              {
                key: 'txt-export',
                label: 'TXT格式',
                children: [
                  { key: 'standard-txt', label: '标准剧本 (TXT)', onClick: () => handleExport('txt', 'standard') },
                  { key: 'director-txt', label: '导演剧本 (TXT)', onClick: () => handleExport('txt', 'director') },
                  { key: 'combined-txt', label: '剧本合集 (TXT)', onClick: () => handleExport('txt', 'combined') },
                ]
              },
              {
                key: 'docx-export',
                label: 'DOCX格式',
                children: [
                  { key: 'standard-docx', label: '标准剧本 (DOCX)', onClick: () => handleExport('docx', 'standard') },
                  { key: 'director-docx', label: '导演剧本 (DOCX)', onClick: () => handleExport('docx', 'director') },
                  { key: 'combined-docx', label: '剧本合集 (DOCX)', onClick: () => handleExport('docx', 'combined') },
                ]
              }
            ]
          }}
          disabled={items.filter(item => item.status === 'completed').length === 0}
        >
          <Button icon={<Download size={16} />}>
            导出结果
          </Button>
        </Dropdown>

        <Button
          icon={<Trash2 size={16} />}
          onClick={handleClear}
          disabled={isGenerating || items.length === 0}
        >
          清除结果
        </Button>
      </div>

      {/* 进度显示 */}
      {(isGenerating || isPaused) && progress.total > 0 && (
        <div className={styles.progressSection}>
          <div className="progress-info">
            <span className="progress-text">
              批量生成进度 {isPaused && <span style={{ color: '#faad14' }}>(已暂停)</span>}
            </span>
            <span className="progress-stats">
              {progress.current} / {progress.total} 已完成
            </span>
          </div>
          <Progress
            percent={progressPercent}
            status={isPaused ? 'normal' : isGenerating ? 'active' : 'normal'}
            strokeColor={isPaused ? '#faad14' : {
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
        </div>
      )}

      {/* 结果显示 */}
      {items.length > 0 && (
        <Card className={styles.resultsContainer} title={`生成结果 (${items.length})`}>
          <div className={styles.resultsList}>
            {items.map((item, index) => (
              <div key={item.id} className="result-item">
                <div className="result-header">
                  <span className="result-title">第 {index + 1} 项</span>
                  <span className={`result-status ${item.status}`}>
                    {getStatusText(item.status)}
                  </span>
                </div>
                <div className="result-content">
                  {item.status === 'failed' ? (
                    <div className="result-error">{item.error || '生成失败'}</div>
                  ) : item.result ? (
                    item.result
                  ) : (
                    <div style={{ color: '#999' }}>等待生成...</div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};

export default SimpleBatchGenerator;
