'use client';

import { useTheme } from 'antd-style';
import { PropsWithChildren, memo, useState, useEffect } from 'react';
import { Flexbox } from 'react-layout-kit';
import ChatHeader from './ChatHeader';

const Workspace = memo<PropsWithChildren>(({ children }) => {
  const theme = useTheme();
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <Flexbox
      flex={1}
      height={'100%'}
      style={{
        background: theme.colorBgContainer,
        overflow: 'hidden',
        position: 'relative',
      }}
      className="workspace-container"
    >
      {/* 只在桌面端显示ChatHeader */}
      {!isMobile && <ChatHeader />}
      <Flexbox
        flex={1}
        style={{
          overflow: 'hidden',
          position: 'relative',
        }}
        data-chat-container
        className="chat-container"
      >
        {children}
      </Flexbox>
    </Flexbox>
  );
});

export default Workspace;
