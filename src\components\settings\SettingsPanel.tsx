'use client';

import React, { useState } from 'react';
import { createStyles } from 'antd-style';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Select, 
  Switch, 
  Divider,
  Space,
  message,
  Tabs,
  InputNumber,
  Alert,
  Typography
} from 'antd';
import { 
  Settings, 
  Key, 
  Palette, 
  Globe, 
  Bell,
  Shield,
  Download,
  Upload,
  Trash2
} from 'lucide-react';
import { ApiConfig, AppSettings, GeminiModel } from '@/types';
import { useSettingsStore } from '@/store/settings';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    height: 100%;
    padding: ${token.paddingLG}px;
    overflow-y: auto;
  `,
  
  tabContent: css`
    padding: ${token.paddingLG}px 0;
  `,
  
  formSection: css`
    margin-bottom: ${token.marginXL}px;
    
    .section-title {
      display: flex;
      align-items: center;
      gap: ${token.marginSM}px;
      margin-bottom: ${token.marginLG}px;
      font-size: ${token.fontSizeLG}px;
      font-weight: 600;
      color: ${token.colorText};
    }
  `,
  
  apiKeyInput: css`
    .ant-input {
      font-family: monospace;
      font-size: ${token.fontSizeSM}px;
    }
  `,
  
  modelCard: css`
    margin-bottom: ${token.marginMD}px;
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
      border-color: ${token.colorPrimary};
    }
    
    &.selected {
      border-color: ${token.colorPrimary};
      background: ${token.colorPrimaryBg};
    }
    
    .model-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: ${token.marginSM}px;
      
      .model-name {
        font-weight: 600;
        color: ${token.colorText};
      }
      
      .model-badge {
        background: ${token.colorSuccessBg};
        color: ${token.colorSuccess};
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
      }
    }
    
    .model-description {
      color: ${token.colorTextSecondary};
      font-size: ${token.fontSizeSM}px;
      margin-bottom: ${token.marginSM}px;
    }
    
    .model-specs {
      display: flex;
      gap: ${token.marginLG}px;
      font-size: ${token.fontSizeSM}px;
      color: ${token.colorTextTertiary};
    }
  `,
  
  dangerZone: css`
    border: 1px solid ${token.colorError};
    border-radius: ${token.borderRadius}px;
    padding: ${token.paddingLG}px;
    background: ${token.colorErrorBg};
    
    .danger-title {
      color: ${token.colorError};
      font-weight: 600;
      margin-bottom: ${token.marginMD}px;
    }
    
    .danger-description {
      color: ${token.colorTextSecondary};
      margin-bottom: ${token.marginLG}px;
    }
  `,
}));

const SettingsPanel: React.FC = () => {
  const { styles } = useStyles();
  const [form] = Form.useForm();
  const [testingApi, setTestingApi] = useState(false);

  // 使用设置状态管理
  const {
    apiConfig,
    appSettings,
    models,
    loading,
    error,
    updateApiConfig,
    updateAppSettings,
    validateApiKey,
    loadModels,
    resetSettings,
  } = useSettingsStore();
  
  // 初始化表单值
  React.useEffect(() => {
    form.setFieldsValue({
      ...apiConfig,
      ...appSettings,
    });
  }, [apiConfig, appSettings, form]);

  // 测试 API Key
  const handleTestApiKey = async () => {
    const apiKey = form.getFieldValue('apiKey');
    if (!apiKey) {
      message.warning('请先输入 API Key');
      return;
    }

    setTestingApi(true);
    try {
      const isValid = await validateApiKey(apiKey);
      if (isValid) {
        message.success('API Key 验证成功！');
        // 加载模型列表
        await loadModels();
      } else {
        message.error('API Key 验证失败，请检查后重试');
      }
    } catch (error) {
      message.error('验证过程中出现错误');
    } finally {
      setTestingApi(false);
    }
  };

  // 保存 API 配置
  const handleSaveApiConfig = async (values: Record<string, unknown>) => {
    try {
      updateApiConfig(values);
      message.success('API 配置保存成功');
    } catch (error) {
      message.error('保存失败，请重试');
    }
  };

  // 保存应用设置
  const handleSaveAppSettings = async (values: Record<string, unknown>) => {
    try {
      updateAppSettings(values);
      message.success('应用设置保存成功');
    } catch (error) {
      message.error('保存失败，请重试');
    }
  };

  // 重置所有设置
  const handleResetSettings = () => {
    resetSettings();
    form.resetFields();
    message.success('设置已重置');
  };

  // 导出设置
  const handleExportSettings = () => {
    const settings = {
      apiConfig: { ...apiConfig, apiKey: '***' }, // 隐藏 API 密钥
      appSettings,
      exportTime: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `gemini-chat-settings-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    message.success('设置导出成功');
  };

  // 清除所有数据
  const handleClearAllData = () => {
    // TODO: 实现清除所有数据的逻辑
    message.success('所有数据已清除');
  };

  const tabItems = [
    {
      key: 'api',
      label: (
        <Space>
          <Key size={16} />
          API 配置
        </Space>
      ),
      children: (
        <div className={styles.tabContent}>
          <Form
            form={form}
            layout="vertical"
            initialValues={apiConfig}
            onFinish={handleSaveApiConfig}
          >
            <div className={styles.formSection}>
              <div className="section-title">
                <Key size={20} />
                Gemini API 配置
              </div>
              
              <Alert
                message="API 密钥安全提示"
                description="您的 API 密钥将安全存储在本地，不会上传到任何服务器。"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />

              <Form.Item
                name="apiKey"
                label="API 密钥"
                rules={[{ required: true, message: '请输入 Gemini API 密钥' }]}
              >
                <Input.Password
                  className={styles.apiKeyInput}
                  placeholder="请输入您的 Gemini API 密钥"
                />
              </Form.Item>

              <Form.Item
                name="apiUrl"
                label="API 地址"
                rules={[{ required: true, message: '请输入 API 地址' }]}
              >
                <Input placeholder="API 地址" />
              </Form.Item>

              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                >
                  保存配置
                </Button>
                <Button
                  onClick={handleTestApiKey}
                  loading={testingApi}
                >
                  测试连接
                </Button>
              </Space>
            </div>

            <Divider />

            <div className={styles.formSection}>
              <div className="section-title">
                <Settings size={20} />
                模型选择
              </div>

              {models.map((model) => (
                <Card
                  key={model.id}
                  size="small"
                  className={`${styles.modelCard} ${apiConfig.model === model.id ? 'selected' : ''}`}
                  onClick={() => {
                    form.setFieldValue('model', model.id);
                    updateApiConfig({ model: model.id });
                  }}
                >
                  <div className="model-header">
                    <span className="model-name">{model.name}</span>
                    {model.supportFiles && (
                      <span className="model-badge">支持文件</span>
                    )}
                  </div>
                  <div className="model-description">{model.description}</div>
                  <div className="model-specs">
                    <span>最大令牌: {model.maxTokens.toLocaleString()}</span>
                  </div>
                </Card>
              ))}
            </div>

            <Divider />

            <div className={styles.formSection}>
              <div className="section-title">
                <Settings size={20} />
                生成参数
              </div>

              <Form.Item
                name="temperature"
                label="温度 (Temperature)"
                tooltip="控制生成文本的随机性，值越高越随机"
              >
                <InputNumber
                  min={0}
                  max={2}
                  step={0.1}
                  style={{ width: '100%' }}
                />
              </Form.Item>

              <Form.Item
                name="maxTokens"
                label="最大令牌数"
                tooltip="限制生成文本的最大长度"
              >
                <InputNumber
                  min={1}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </div>
          </Form>
        </div>
      ),
    },
    {
      key: 'appearance',
      label: (
        <Space>
          <Palette size={16} />
          外观设置
        </Space>
      ),
      children: (
        <div className={styles.tabContent}>
          <Form
            layout="vertical"
            initialValues={appSettings}
            onFinish={handleSaveAppSettings}
          >
            <div className={styles.formSection}>
              <div className="section-title">
                <Palette size={20} />
                主题设置
              </div>

              <Form.Item
                name="theme"
                label="主题模式"
              >
                <Select>
                  <Option value="light">浅色模式</Option>
                  <Option value="dark">深色模式</Option>
                  <Option value="auto">跟随系统</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="fontSize"
                label="字体大小"
              >
                <Select>
                  <Option value="small">小</Option>
                  <Option value="medium">中</Option>
                  <Option value="large">大</Option>
                </Select>
              </Form.Item>
            </div>

            <Divider />

            <div className={styles.formSection}>
              <div className="section-title">
                <Globe size={20} />
                语言设置
              </div>

              <Form.Item
                name="language"
                label="界面语言"
              >
                <Select>
                  <Option value="zh-CN">简体中文</Option>
                  <Option value="en-US">English</Option>
                </Select>
              </Form.Item>
            </div>

            <Button type="primary" htmlType="submit" loading={loading}>
              保存设置
            </Button>
          </Form>
        </div>
      ),
    },
    {
      key: 'general',
      label: (
        <Space>
          <Settings size={16} />
          通用设置
        </Space>
      ),
      children: (
        <div className={styles.tabContent}>
          <Form
            layout="vertical"
            initialValues={appSettings}
            onFinish={handleSaveAppSettings}
          >
            <div className={styles.formSection}>
              <div className="section-title">
                <Bell size={20} />
                功能设置
              </div>

              <Form.Item
                name="autoSave"
                label="自动保存"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="notifications"
                label="桌面通知"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </div>

            <Button type="primary" htmlType="submit" loading={loading}>
              保存设置
            </Button>
          </Form>
        </div>
      ),
    },
    {
      key: 'data',
      label: (
        <Space>
          <Shield size={16} />
          数据管理
        </Space>
      ),
      children: (
        <div className={styles.tabContent}>
          <div className={styles.formSection}>
            <div className="section-title">
              <Download size={20} />
              数据导出
            </div>
            
            <Text type="secondary">
              导出您的设置配置，以便在其他设备上使用或作为备份。
            </Text>
            
            <div style={{ marginTop: 16 }}>
              <Button
                icon={<Download size={16} />}
                onClick={handleExportSettings}
              >
                导出设置
              </Button>
            </div>
          </div>

          <Divider />

          <div className={styles.formSection}>
            <div className="section-title">
              <Upload size={20} />
              数据导入
            </div>
            
            <Text type="secondary">
              从备份文件中恢复您的设置配置。
            </Text>
            
            <div style={{ marginTop: 16 }}>
              <Button icon={<Upload size={16} />}>
                导入设置
              </Button>
            </div>
          </div>

          <Divider />

          <div className={styles.dangerZone}>
            <div className="danger-title">危险操作</div>
            <div className="danger-description">
              以下操作将永久删除您的所有数据，包括聊天记录、提示词和设置。此操作不可撤销。
            </div>
            <Button
              danger
              icon={<Trash2 size={16} />}
              onClick={handleClearAllData}
            >
              清除所有数据
            </Button>
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Title level={2}>设置</Title>
      <Tabs
        items={tabItems}
        size="large"
        tabPosition="left"
      />
    </div>
  );
};

export default SettingsPanel;