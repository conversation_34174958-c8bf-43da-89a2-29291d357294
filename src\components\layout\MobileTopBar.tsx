'use client';

import React, { useState } from 'react';
import { Button } from 'antd';
import { createStyles } from 'antd-style';
import { Menu } from 'lucide-react';
import Image from 'next/image';
import MobileNavDrawer from './MobileNavDrawer';

const useStyles = createStyles(({ token, css }) => ({
  mobileTopBar: css`
    display: none;

    @media (max-width: 768px) {
      display: flex !important;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: ${token.colorBgContainer};
      border-bottom: 1px solid ${token.colorBorderSecondary};
      min-height: 56px;
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      z-index: 9999 !important;
      width: 100% !important;
      box-sizing: border-box;
    }
  `,
  
  leftSection: css`
    display: flex;
    align-items: center;
    gap: 12px;
  `,
  
  menuButton: css`
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: ${token.colorBgContainer};
    border: 1px solid ${token.colorBorderSecondary};
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      background: ${token.colorBgTextHover};
      border-color: ${token.colorPrimary};
    }
    
    &:active {
      transform: scale(0.95);
    }
  `,
  
  brandInfo: css`
    display: flex;
    align-items: center;
    gap: 8px;
    
    .logo {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      overflow: hidden;
    }
    
    .title {
      font-size: 16px;
      font-weight: 600;
      color: ${token.colorText};
    }
  `,
  
  rightSection: css`
    display: flex;
    align-items: center;
    gap: 8px;
  `,
}));

interface MobileTopBarProps {
  title?: string;
  rightActions?: React.ReactNode;
}

const MobileTopBar: React.FC<MobileTopBarProps> = ({
  title = '微甜 AI Studio',
  rightActions
}) => {
  const { styles } = useStyles();
  const [drawerVisible, setDrawerVisible] = useState(false);

  const handleToggleDrawer = () => {
    setDrawerVisible(!drawerVisible);
  };

  const handleCloseDrawer = () => {
    setDrawerVisible(false);
  };

  return (
    <>
      <div className={styles.mobileTopBar}>
        <div className={styles.leftSection}>
          <Button
            type="text"
            icon={<Menu size={20} />}
            className={styles.menuButton}
            onClick={handleToggleDrawer}
            aria-label="打开菜单"
          />

          <div className={styles.brandInfo}>
            <div className="logo">
              <Image
                src="/favicon.png"
                alt="Logo"
                width={24}
                height={24}
                style={{ objectFit: 'cover', borderRadius: '4px' }}
              />
            </div>
            <div className="title">{title}</div>
          </div>
        </div>

        {rightActions && (
          <div className={styles.rightSection}>
            {rightActions}
          </div>
        )}
      </div>
      
      <MobileNavDrawer
        visible={drawerVisible}
        onClose={handleCloseDrawer}
      />
    </>
  );
};

export default MobileTopBar;
