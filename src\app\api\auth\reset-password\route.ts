import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import bcrypt from 'bcryptjs';

// 密码强度验证
const validatePassword = (password: string): string | null => {
  if (password.length < 8) {
    return '密码长度至少为8位';
  }
  if (!/(?=.*[a-z])/.test(password)) {
    return '密码必须包含至少一个小写字母';
  }
  if (!/(?=.*[A-Z])/.test(password)) {
    return '密码必须包含至少一个大写字母';
  }
  if (!/(?=.*\d)/.test(password)) {
    return '密码必须包含至少一个数字';
  }
  return null;
};

// POST - 重置密码
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token, password, confirmPassword } = body;

    // 基础验证
    if (!token || !password || !confirmPassword) {
      return NextResponse.json(
        {
          success: false,
          error: '所有字段都是必填的',
        },
        { status: 400 }
      );
    }

    // 确认密码验证
    if (password !== confirmPassword) {
      return NextResponse.json(
        {
          success: false,
          error: '两次输入的密码不一致',
        },
        { status: 400 }
      );
    }

    // 密码强度验证
    const passwordError = validatePassword(password);
    if (passwordError) {
      return NextResponse.json(
        {
          success: false,
          error: passwordError,
        },
        { status: 400 }
      );
    }

    // 查找重置token
    const resetTokens = await executeQuery(
      'SELECT user_id, expires_at FROM password_reset_tokens WHERE token = ?',
      [token]
    );

    if (resetTokens.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '无效的重置链接',
        },
        { status: 400 }
      );
    }

    const resetToken = resetTokens[0];

    // 检查token是否过期
    const now = new Date();
    const expiresAt = new Date(resetToken.expires_at);
    
    if (now > expiresAt) {
      // 删除过期的token
      await executeQuery(
        'DELETE FROM password_reset_tokens WHERE token = ?',
        [token]
      );
      
      return NextResponse.json(
        {
          success: false,
          error: '重置链接已过期，请重新申请重置密码',
        },
        { status: 400 }
      );
    }

    // 查找用户
    const users = await executeQuery(
      'SELECT id, username, email FROM users WHERE id = ?',
      [resetToken.user_id]
    );

    if (users.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '用户不存在',
        },
        { status: 400 }
      );
    }

    const user = users[0];

    // 加密新密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 更新用户密码
    await executeQuery(
      'UPDATE users SET password_hash = ?, updated_at = ? WHERE id = ?',
      [passwordHash, new Date(), user.id]
    );

    // 删除已使用的重置token
    await executeQuery(
      'DELETE FROM password_reset_tokens WHERE token = ?',
      [token]
    );

    // 删除该用户的所有其他重置token（如果有的话）
    await executeQuery(
      'DELETE FROM password_reset_tokens WHERE user_id = ?',
      [user.id]
    );

    return NextResponse.json({
      success: true,
      message: '密码重置成功，请使用新密码登录',
      data: {
        username: user.username,
        email: user.email,
      },
    });
  } catch (error) {
    console.error('重置密码失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '重置密码失败，请重试',
      },
      { status: 500 }
    );
  }
}

// GET - 验证重置token是否有效
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        {
          success: false,
          error: '重置token不能为空',
        },
        { status: 400 }
      );
    }

    // 查找重置token
    const resetTokens = await executeQuery(
      'SELECT user_id, expires_at FROM password_reset_tokens WHERE token = ?',
      [token]
    );

    if (resetTokens.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '无效的重置链接',
        },
        { status: 400 }
      );
    }

    const resetToken = resetTokens[0];

    // 检查token是否过期
    const now = new Date();
    const expiresAt = new Date(resetToken.expires_at);
    
    if (now > expiresAt) {
      // 删除过期的token
      await executeQuery(
        'DELETE FROM password_reset_tokens WHERE token = ?',
        [token]
      );
      
      return NextResponse.json(
        {
          success: false,
          error: '重置链接已过期，请重新申请重置密码',
        },
        { status: 400 }
      );
    }

    // 查找用户信息
    const users = await executeQuery(
      'SELECT username, email FROM users WHERE id = ?',
      [resetToken.user_id]
    );

    if (users.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '用户不存在',
        },
        { status: 400 }
      );
    }

    const user = users[0];

    return NextResponse.json({
      success: true,
      message: '重置链接有效',
      data: {
        username: user.username,
        email: user.email,
      },
    });
  } catch (error) {
    console.error('验证重置token失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '验证失败，请重试',
      },
      { status: 500 }
    );
  }
}
