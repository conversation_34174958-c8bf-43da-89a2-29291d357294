'use client';

import React, { useState } from 'react';
import { Button } from 'antd';
import { createStyles } from 'antd-style';
import { Menu } from 'lucide-react';
import MobileNavDrawer from './MobileNavDrawer';

const useStyles = createStyles(({ token, css }) => ({
  mobileMenuButton: css`
    position: fixed;
    top: 16px;
    left: 16px;
    z-index: 1000;
    width: 44px;
    height: 44px;
    border-radius: 8px;
    background: ${token.colorBgContainer};
    border: 1px solid ${token.colorBorderSecondary};
    box-shadow: ${token.boxShadow};
    display: none;
    align-items: center;
    justify-content: center;
    
    @media (max-width: 768px) {
      display: flex;
    }
    
    &:hover {
      background: ${token.colorBgTextHover};
      border-color: ${token.colorPrimary};
    }
    
    &:active {
      transform: scale(0.95);
    }
  `,
}));

const MobileMenuButton: React.FC = () => {
  const { styles } = useStyles();
  const [drawerVisible, setDrawerVisible] = useState(false);

  const handleToggleDrawer = () => {
    setDrawerVisible(!drawerVisible);
  };

  const handleCloseDrawer = () => {
    setDrawerVisible(false);
  };

  return (
    <>
      <Button
        type="text"
        icon={<Menu size={20} />}
        className={styles.mobileMenuButton}
        onClick={handleToggleDrawer}
        aria-label="打开菜单"
      />
      
      <MobileNavDrawer
        visible={drawerVisible}
        onClose={handleCloseDrawer}
      />
    </>
  );
};

export default MobileMenuButton;
