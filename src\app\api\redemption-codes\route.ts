import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import { generateId } from '@/lib/utils';
import { 
  generateBatchCodes, 
  RedemptionCodeType,
  RedemptionCode,
  RedemptionBatch 
} from '@/lib/redemption-code';

// GET - 获取兑换码列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get('agentId');
    const batchId = searchParams.get('batchId');
    const type = searchParams.get('type'); // 'codes' | 'batches'

    if (!agentId) {
      return NextResponse.json(
        {
          success: false,
          error: '智能体ID为必填项',
        },
        { status: 400 }
      );
    }

    if (type === 'batches') {
      // 获取兑换码批次列表
      const query = `
        SELECT * FROM redemption_batches
        WHERE agent_id = ?
        ORDER BY created_at DESC
      `;
      
      const rawBatches = await executeQuery<any[]>(query, [agentId]);
      
      const batches = rawBatches.map(batch => ({
        id: batch.id,
        agentId: batch.agent_id,
        batchName: batch.batch_name,
        codeType: batch.code_type,
        label: batch.label,
        totalCount: batch.total_count,
        usedCount: batch.used_count,
        durationDays: batch.duration_days,
        usageCount: batch.usage_count,
        expiresAt: batch.expires_at,
        createdBy: batch.created_by,
        createdAt: batch.created_at,
        updatedAt: batch.updated_at,
      }));

      return NextResponse.json({
        success: true,
        data: batches,
      });
    } else {
      // 获取兑换码列表
      let query = `
        SELECT rc.*, rb.batch_name, rb.label as batch_label
        FROM redemption_codes rc
        LEFT JOIN redemption_batches rb ON rc.batch_id = rb.id
        WHERE rc.agent_id = ?
      `;
      
      const params = [agentId];
      
      if (batchId) {
        query += ' AND rc.batch_id = ?';
        params.push(batchId);
      }
      
      query += ' ORDER BY rc.created_at DESC';
      
      const rawCodes = await executeQuery<any[]>(query, params);
      
      const codes = rawCodes.map(code => ({
        id: code.id,
        agentId: code.agent_id,
        code: code.code,
        codeType: code.code_type,
        label: code.label,
        durationDays: code.duration_days,
        usageCount: code.usage_count,
        hashCode: code.hash_code,
        salt: code.salt,
        isUsed: Boolean(code.is_used),
        usedAt: code.used_at,
        usedBy: code.used_by,
        expiresAt: code.expires_at,
        createdBy: code.created_by,
        createdAt: code.created_at,
        updatedAt: code.updated_at,
        batchId: code.batch_id,
        batchName: code.batch_name,
        batchLabel: code.batch_label,
        isVoided: Boolean(code.is_voided),
        voidedAt: code.voided_at,
        voidedBy: code.voided_by,
      }));

      return NextResponse.json({
        success: true,
        data: codes,
      });
    }

  } catch (error) {
    console.error('获取兑换码失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '获取兑换码失败',
      },
      { status: 500 }
    );
  }
}

// POST - 生成兑换码
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      agentId,
      codeType,
      label,
      count,
      durationDays,
      usageCount,
      expiresAt,
      createdBy
    } = body;

    if (!agentId || !codeType || !label || !count || !createdBy) {
      return NextResponse.json(
        {
          success: false,
          error: '智能体ID、兑换码类型、标签、数量和创建者为必填项',
        },
        { status: 400 }
      );
    }

    // 验证兑换码类型和必填字段
    if (codeType === 'duration' && !durationDays) {
      return NextResponse.json(
        {
          success: false,
          error: '时长兑换需要提供兑换天数',
        },
        { status: 400 }
      );
    }

    if (codeType === 'usage' && !usageCount) {
      return NextResponse.json(
        {
          success: false,
          error: '次数兑换需要提供兑换次数',
        },
        { status: 400 }
      );
    }

    if (count > 1000) {
      return NextResponse.json(
        {
          success: false,
          error: '单次生成兑换码数量不能超过1000个',
        },
        { status: 400 }
      );
    }

    // 生成兑换码
    const expirationDate = expiresAt ? new Date(expiresAt) : undefined;
    const { codes, batchInfo } = generateBatchCodes(
      count,
      agentId,
      codeType as RedemptionCodeType,
      label,
      durationDays,
      usageCount,
      expirationDate
    );

    // 创建批次记录
    const batchId = generateId();
    const now = new Date();
    
    const batchQuery = `
      INSERT INTO redemption_batches (
        id, agent_id, batch_name, code_type, label, total_count,
        duration_days, usage_count, expires_at, created_by, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const batchName = `${label}_${now.toISOString().split('T')[0]}_${count}个`;
    
    await executeQuery(batchQuery, [
      batchId,
      agentId,
      batchName,
      codeType,
      label,
      count,
      durationDays || null,
      usageCount || null,
      expirationDate || null,
      createdBy,
      now,
      now,
    ]);

    // 批量插入兑换码
    const codeQuery = `
      INSERT INTO redemption_codes (
        id, agent_id, code, code_type, label, duration_days, usage_count,
        hash_code, salt, expires_at, created_by, created_at, updated_at, batch_id
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    for (const codeData of codes) {
      const codeId = generateId();
      await executeQuery(codeQuery, [
        codeId,
        agentId,
        codeData.code,
        codeType,
        label,
        durationDays || null,
        usageCount || null,
        codeData.hashCode,
        codeData.salt,
        expirationDate || null,
        createdBy,
        now,
        now,
        batchId,
      ]);
    }

    return NextResponse.json({
      success: true,
      data: {
        batchId,
        batchName,
        totalCount: count,
        codes: codes.map(c => c.code),
      },
      message: `成功生成 ${count} 个兑换码`,
    });

  } catch (error) {
    console.error('生成兑换码失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '生成兑换码失败',
      },
      { status: 500 }
    );
  }
}

// DELETE - 删除兑换码批次
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const batchId = searchParams.get('batchId');

    if (!batchId) {
      return NextResponse.json(
        {
          success: false,
          error: '批次ID为必填项',
        },
        { status: 400 }
      );
    }

    // 检查是否有已使用的兑换码
    const usedCodesQuery = `
      SELECT COUNT(*) as used_count
      FROM redemption_codes
      WHERE batch_id = ? AND is_used = true
    `;

    const usedResult = await executeQuery<any[]>(usedCodesQuery, [batchId]);
    const usedCount = usedResult[0]?.used_count || 0;

    if (usedCount > 0) {
      return NextResponse.json(
        {
          success: false,
          error: `该批次中有 ${usedCount} 个兑换码已被使用，无法删除`,
        },
        { status: 400 }
      );
    }

    // 删除兑换码和批次（外键约束会自动删除相关兑换码）
    const deleteBatchQuery = `DELETE FROM redemption_batches WHERE id = ?`;
    await executeQuery(deleteBatchQuery, [batchId]);

    return NextResponse.json({
      success: true,
      message: '兑换码批次删除成功',
    });

  } catch (error) {
    console.error('删除兑换码批次失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '删除兑换码批次失败',
      },
      { status: 500 }
    );
  }
}
