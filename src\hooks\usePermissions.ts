import { useAuthStore } from '@/store/auth';

// 用户角色枚举
export enum UserRole {
  USER = 'user',
  ADMIN = 'admin'
}

// 权限枚举
export enum Permission {
  VIEW_SETTINGS = 'view_settings',
  MANAGE_PROMPTS = 'manage_prompts',
  MANAGE_AGENTS = 'manage_agents',
  MANAGE_USERS = 'manage_users'
}

// 角色权限映射
const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.USER]: [
    Permission.VIEW_SETTINGS  // 普通用户也可以访问设置页面
  ],
  [UserRole.ADMIN]: [
    Permission.VIEW_SETTINGS,
    Permission.MANAGE_PROMPTS,
    Permission.MANAGE_AGENTS,
    Permission.MANAGE_USERS
  ]
};

/**
 * 权限管理 Hook
 */
export const usePermissions = () => {
  const { user } = useAuthStore();

  /**
   * 检查用户是否有指定权限
   */
  const hasPermission = (permission: Permission): boolean => {
    if (!user || !user.role) {
      return false;
    }

    const userRole = user.role as UserRole;
    const permissions = ROLE_PERMISSIONS[userRole] || [];
    return permissions.includes(permission);
  };

  /**
   * 检查用户是否为管理员
   */
  const isAdmin = (): boolean => {
    return user?.role === UserRole.ADMIN;
  };

  /**
   * 检查用户是否为普通用户
   */
  const isUser = (): boolean => {
    return user?.role === UserRole.USER;
  };

  /**
   * 检查是否可以查看设置页面
   */
  const canViewSettings = (): boolean => {
    return hasPermission(Permission.VIEW_SETTINGS);
  };

  /**
   * 检查是否可以管理提示词
   */
  const canManagePrompts = (): boolean => {
    return hasPermission(Permission.MANAGE_PROMPTS);
  };

  /**
   * 检查是否可以管理智能体
   */
  const canManageAgents = (): boolean => {
    return hasPermission(Permission.MANAGE_AGENTS);
  };

  /**
   * 检查是否可以管理用户
   */
  const canManageUsers = (): boolean => {
    return hasPermission(Permission.MANAGE_USERS);
  };

  return {
    hasPermission,
    isAdmin,
    isUser,
    canViewSettings,
    canManagePrompts,
    canManageAgents,
    canManageUsers,
    userRole: user?.role as UserRole
  };
};
