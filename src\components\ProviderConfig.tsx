import React, { useState, useEffect } from 'react';
import { Card, Input, InputNumber, Button, Switch, Select, Space, Typography, Divider, Tag, Tooltip, Alert, App } from 'antd';
import { EyeInvisibleOutlined, EyeTwoTone, SettingOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { AIProvider, AI_PROVIDERS } from '@/types/ai-provider';
import { useAIProviderConfigsStore } from '@/store/aiProviderConfigs';
import { useAuthStore } from '@/store/auth';
import { createStyles } from 'antd-style';

const { Title, Text } = Typography;
const { Option } = Select;

const useStyles = createStyles(({ token, css }) => ({
  providerCard: css`
    margin-bottom: 16px;

    @media (max-width: 768px) {
      margin-bottom: 12px;

      .ant-card-head {
        padding: 12px 16px;
      }

      .ant-card-body {
        padding: 16px;
      }

      .ant-card-head-title {
        font-size: 14px;
      }
    }
  `,

  configSpace: css`
    width: 100%;

    @media (max-width: 768px) {
      .ant-space-item {
        width: 100%;
      }

      .ant-input,
      .ant-input-password,
      .ant-select {
        width: 100% !important;
      }
    }
  `,
}));

interface ProviderConfigProps {
  provider: AIProvider;
}

export const ProviderConfigComponent: React.FC<ProviderConfigProps> = ({ provider }) => {
  const { styles } = useStyles();
  const { message } = App.useApp();
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<'success' | 'error' | null>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [localConfig, setLocalConfig] = useState({
    enabled: false,
    apiKey: '',
    baseUrl: '',
    defaultModel: '',
    temperature: 0.7,
    maxTokens: 4096,
  });
  const [modelSuffix, setModelSuffix] = useState('');

  const { user } = useAuthStore();
  const {
    loading,
    error,
    loadConfigs,
    saveConfig,
    deleteConfig,
    getProviderConfig,
    clearError,
  } = useAIProviderConfigsStore();

  const providerInfo = AI_PROVIDERS[provider];
  const currentConfig = getProviderConfig(provider);

  // 加载配置
  useEffect(() => {
    if (user?.id) {
      loadConfigs(user.id);
    }
  }, [user?.id, loadConfigs]);

  // 更新本地配置
  useEffect(() => {
    if (currentConfig) {
      const savedModel = currentConfig.defaultModel || (providerInfo.models[0]?.id || '');

      // 对于 Anthropic 提供商，尝试分离基础模型和后缀
      let baseModel = savedModel;
      let suffix = '';

      if (provider === 'anthropic' && savedModel) {
        // 查找是否有匹配的基础模型
        const matchingBaseModel = providerInfo.models.find(model =>
          savedModel.startsWith(model.id)
        );

        if (matchingBaseModel && savedModel.length > matchingBaseModel.id.length) {
          baseModel = matchingBaseModel.id;
          suffix = savedModel.substring(matchingBaseModel.id.length); // 保留完整后缀包括 '-'
        }
      }

      setLocalConfig({
        enabled: currentConfig.enabled,
        apiKey: currentConfig.hasApiKey ? '••••••••••••••••' : '', // 显示掩码或空值
        baseUrl: currentConfig.baseUrl || providerInfo.defaultBaseUrl,
        defaultModel: baseModel,
        temperature: currentConfig.temperature,
        maxTokens: currentConfig.maxTokens,
      });
      // 从extraConfig中读取完整API URL
      const savedFullApiUrl = currentConfig.extraConfig?.fullApiUrl;
      if (savedFullApiUrl) {
        setModelSuffix(savedFullApiUrl);
      } else {
        // 为不同提供商设置默认完整API URL
        const baseUrl = currentConfig.baseUrl || providerInfo.defaultBaseUrl;
        if (provider === 'anthropic') {
          setModelSuffix(`${baseUrl}/chat/completions`);
        } else if (provider === 'openai') {
          setModelSuffix(`${baseUrl}/chat/completions`);
        } else if (provider === 'google') {
          setModelSuffix(`${baseUrl}/v1beta/models`);
        } else if (provider === 'deepseek') {
          setModelSuffix(`${baseUrl}/chat/completions`);
        } else {
          setModelSuffix(baseUrl);
        }
      }
    } else {
      const defaultModel = providerInfo.models[0]?.id || '';
      setLocalConfig({
        enabled: false,
        apiKey: '',
        baseUrl: providerInfo.defaultBaseUrl,
        defaultModel: defaultModel,
        temperature: 0.7,
        maxTokens: 4096,
      });
      // 为不同提供商设置默认完整API URL
      const baseUrl = providerInfo.defaultBaseUrl;
      if (provider === 'anthropic') {
        setModelSuffix(`${baseUrl}/chat/completions`);
      } else if (provider === 'openai') {
        setModelSuffix(`${baseUrl}/chat/completions`);
      } else if (provider === 'google') {
        setModelSuffix(`${baseUrl}/v1beta/models`);
      } else if (provider === 'deepseek') {
        setModelSuffix(`${baseUrl}/chat/completions`);
      } else {
        setModelSuffix(baseUrl);
      }
    }
  }, [currentConfig, providerInfo, provider]);

  const handleSave = async () => {
    if (!user?.id) {
      message.error('请先登录');
      return;
    }

    // 检查API密钥是否为掩码（表示未修改）
    const isApiKeyMasked = localConfig.apiKey === '••••••••••••••••';

    // 如果API密钥为空或为掩码，且当前配置没有API密钥，则提示用户
    if (!localConfig.apiKey || (isApiKeyMasked && !currentConfig?.hasApiKey)) {
      message.error('请输入有效的 API Key');
      return;
    }

    try {
      const configToSave = {
        userId: user.id,
        provider,
        enabled: localConfig.enabled,
        baseUrl: localConfig.baseUrl,
        defaultModel: localConfig.defaultModel,
        temperature: localConfig.temperature,
        maxTokens: localConfig.maxTokens,
        extraConfig: {
          fullApiUrl: modelSuffix
        }
      };

      // 只有在用户输入了新的API密钥时才包含apiKey字段
      if (!isApiKeyMasked && localConfig.apiKey) {
        (configToSave as any).apiKey = localConfig.apiKey;
      }

      await saveConfig(configToSave);
      message.success('配置保存成功');

      // 重新加载配置以获取最新状态
      if (user?.id) {
        loadConfigs(user.id);
      }
    } catch (error) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
    }
  };

  const handleReset = async () => {
    if (!user?.id) {
      message.error('请先登录');
      return;
    }

    try {
      await deleteConfig(user.id, provider);
      message.success('配置重置成功');
    } catch (error) {
      console.error('重置配置失败:', error);
      message.error('重置配置失败');
    }
  };

  const handleTest = async () => {
    if (!localConfig.apiKey) {
      message.error('请先输入 API Key');
      return;
    }

    setTesting(true);
    setTestResult(null);
    setErrorMessage('');

    try {
      // 调用真实的API测试接口
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

      const response = await fetch('/api/test-ai-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider,
          apiKey: localConfig.apiKey,
          baseUrl: localConfig.baseUrl || '',
          model: localConfig.defaultModel || '',
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        setTestResult('success');
        message.success('连接测试成功');
      } else {
        setTestResult('error');
        setErrorMessage(result.error || '连接测试失败');
        message.error(result.error || '连接测试失败');
      }
    } catch (error) {
      setTestResult('error');
      let errorMsg = '连接测试失败';

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMsg = '请求超时，请检查网络连接';
        } else {
          errorMsg = error.message;
        }
      }

      setErrorMessage(errorMsg);
      message.error(errorMsg);
      console.error('API测试错误:', error);
    } finally {
      setTesting(false);
    }
  };

  const updateLocalConfig = (key: string, value: any) => {
    setLocalConfig(prev => ({
      ...prev,
      [key]: value,
    }));

    // 当选择模型时，保持当前的API路径后缀不变
    // 用户可以手动编辑API路径后缀
  };

  return (
    <Card
      title={
        <Space>
          <span style={{ fontSize: '18px' }}>{providerInfo.icon}</span>
          <div>
            <Title level={4} style={{ margin: 0 }}>
              {providerInfo.name}
            </Title>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {providerInfo.description}
            </Text>
          </div>
        </Space>
      }
      extra={
        <Switch
          checked={localConfig.enabled}
          onChange={(checked) => updateLocalConfig('enabled', checked)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      }
      className={styles.providerCard}
      loading={loading}
    >
      {error && (
        <Alert
          message={error}
          type="error"
          closable
          onClose={clearError}
          style={{ marginBottom: 16 }}
        />
      )}

      <Space direction="vertical" className={styles.configSpace} size="middle">
        {/* API Key */}
        <div>
          <Text strong>API Key</Text>
          <Input.Password
            placeholder={`请输入${providerInfo.name} API Key`}
            value={localConfig.apiKey}
            onChange={(e) => updateLocalConfig('apiKey', e.target.value)}
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            style={{ marginTop: 4 }}
          />
        </div>

        {/* API URL */}
        <div>
          <Text strong>API URL</Text>
          <Input
            placeholder={`${providerInfo.name} API 地址`}
            value={localConfig.baseUrl}
            onChange={(e) => updateLocalConfig('baseUrl', e.target.value)}
            style={{ marginTop: 4 }}
          />
        </div>

        {/* 默认模型 */}
        <div>
          <Text strong>默认模型</Text>
          <Select
            style={{ width: '100%', marginTop: 4 }}
            value={localConfig.defaultModel}
            onChange={(value) => updateLocalConfig('defaultModel', value)}
            placeholder="选择默认模型"
          >
            {providerInfo.models.map((model) => (
              <Option key={model.id} value={model.id}>
                <Space>
                  <span>{model.name}</span>
                  <Tag color="blue">
                    {model.maxTokens.toLocaleString()} tokens
                  </Tag>
                </Space>
              </Option>
            ))}
          </Select>
        </div>

        {/* 完整API请求URL编辑 */}
        <div>
          <Text strong>完整API请求URL</Text>
          <Input
            style={{ width: '100%', marginTop: 4 }}
            value={modelSuffix}
            onChange={(e) => setModelSuffix(e.target.value)}
            placeholder="例如: https://www.dpapi.top/v1/chat/completions"
          />
        </div>

        {/* 生成参数 */}
        <div>
          <Text strong>温度 (Temperature)</Text>
          <InputNumber
            style={{ width: '100%', marginTop: 4 }}
            min={0}
            max={2}
            step={0.1}
            value={localConfig.temperature}
            onChange={(value) => updateLocalConfig('temperature', value || 0.7)}
            placeholder="控制生成文本的随机性，值越高越随机"
          />
        </div>

        <div>
          <Text strong>最大令牌数 (Max Tokens)</Text>
          <InputNumber
            style={{ width: '100%', marginTop: 4 }}
            min={1}
            value={localConfig.maxTokens}
            onChange={(value) => updateLocalConfig('maxTokens', value || 4096)}
            placeholder="限制生成文本的最大长度"
          />
        </div>

        <Divider />

        {/* 操作按钮 */}
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Button
            type="primary"
            icon={<SettingOutlined />}
            onClick={handleTest}
            loading={testing}
            disabled={!localConfig.apiKey}
          >
            测试连接
          </Button>
          <Space>
            <Button onClick={handleReset}>
              重置配置
            </Button>
            <Button type="primary" onClick={handleSave}>
              保存配置
            </Button>
          </Space>
        </Space>

        {/* 测试结果 */}
        {testResult && (
          <Alert
            message={testResult === 'success' ? '连接测试成功' : errorMessage}
            type={testResult === 'success' ? 'success' : 'error'}
            icon={testResult === 'success' ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
            showIcon
          />
        )}

        {/* 可用模型 */}
        <div>
          <Text strong>可用模型</Text>
          <div style={{ marginTop: 8 }}>
            <Space wrap>
              {providerInfo.models.map((model) => (
                <Tooltip
                  key={model.id}
                  title={
                    <div>
                      <div>{model.description}</div>
                      <div>最大令牌: {model.maxTokens.toLocaleString()}</div>
                      <div>输入价格: ${model.inputPrice}/1K tokens</div>
                      <div>输出价格: ${model.outputPrice}/1K tokens</div>
                    </div>
                  }
                >
                  <Tag color="blue" style={{ cursor: 'pointer' }}>
                    {model.name} 📎
                  </Tag>
                </Tooltip>
              ))}
            </Space>
          </div>
        </div>
      </Space>
    </Card>
  );
};
