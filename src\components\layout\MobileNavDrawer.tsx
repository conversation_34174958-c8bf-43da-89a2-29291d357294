'use client';

import React from 'react';
import { Drawer, Avatar } from 'antd';
import { createStyles } from 'antd-style';
import { MessageSquare, Bot, Settings, Book, User, LogOut } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuthStore } from '@/store/auth';
import { usePermissions } from '@/hooks/usePermissions';
import Image from 'next/image';

const useStyles = createStyles(({ token, css }) => ({
  drawerContent: css`
    padding: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
  `,
  header: css`
    padding: 20px;
    border-bottom: 1px solid ${token.colorBorderSecondary};
    background: ${token.colorBgContainer};
  `,
  brandInfo: css`
    display: flex;
    align-items: center;
    gap: 12px;
    
    .logo {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }
    
    .title {
      font-size: 18px;
      font-weight: 600;
      color: ${token.colorText};
      margin: 0;
    }
  `,
  navigation: css`
    flex: 1;
    padding: 16px 0;
  `,
  navItem: css`
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: ${token.colorText};
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    
    &:hover {
      background: ${token.colorBgTextHover};
    }
    
    &.active {
      background: ${token.colorPrimaryBg};
      color: ${token.colorPrimary};
      border-right: 3px solid ${token.colorPrimary};
    }
    
    .icon {
      width: 20px;
      height: 20px;
    }
    
    .text {
      font-size: 16px;
      font-weight: 500;
    }
  `,
  footer: css`
    border-top: 1px solid ${token.colorBorderSecondary};
    padding: 16px 20px;
  `,
  userInfo: css`
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    
    .info {
      flex: 1;
      
      .name {
        font-size: 16px;
        font-weight: 500;
        color: ${token.colorText};
        margin: 0;
      }
      
      .email {
        font-size: 14px;
        color: ${token.colorTextSecondary};
        margin: 0;
      }
    }
  `,
  logoutButton: css`
    display: flex;
    align-items: center;
    gap: 8px;
    color: ${token.colorError};
    background: none;
    border: none;
    padding: 8px 0;
    cursor: pointer;
    font-size: 14px;
    
    &:hover {
      color: ${token.colorErrorHover};
    }
  `,
}));

interface MobileNavDrawerProps {
  visible: boolean;
  onClose: () => void;
}

const MobileNavDrawer: React.FC<MobileNavDrawerProps> = ({ visible, onClose }) => {
  const { styles } = useStyles();
  const router = useRouter();
  const pathname = usePathname();
  const { user, logout } = useAuthStore();
  const { canViewSettings } = usePermissions();

  const navigationItems = [
    {
      key: '/chat',
      icon: MessageSquare,
      text: '智能对话',
      path: '/chat',
    },
    {
      key: '/agents',
      icon: Bot,
      text: '智能体',
      path: '/agents',
    },
    {
      key: '/settings',
      icon: Settings,
      text: '设置',
      path: '/settings',
      requiresPermission: true,
    },
    {
      key: '/help',
      icon: Book,
      text: '帮助文档',
      path: '/help',
    },
  ];

  // 根据权限过滤导航项
  const filteredItems = navigationItems.filter(item =>
    !item.requiresPermission || canViewSettings()
  );

  const handleNavigation = (path: string) => {
    router.push(path);
    onClose();
  };

  const handleLogout = () => {
    logout();
    router.push('/login');
    onClose();
  };

  return (
    <Drawer
      title={null}
      placement="left"
      onClose={onClose}
      open={visible}
      width={280}
      styles={{
        body: { padding: 0 },
        header: { display: 'none' },
      }}
      className={styles.drawerContent}
    >
      {/* 头部品牌信息 */}
      <div className={styles.header}>
        <div className={styles.brandInfo}>
          <div className="logo">
            <Image
              src="/favicon.png"
              alt="微甜 AI Studio"
              width={40}
              height={40}
              style={{ borderRadius: '8px' }}
            />
          </div>
          <h2 className="title">微甜 AI Studio</h2>
        </div>
      </div>

      {/* 导航菜单 */}
      <div className={styles.navigation}>
        {filteredItems.map((item) => {
          const IconComponent = item.icon;
          const isActive = pathname === item.path || pathname.startsWith(item.path);
          
          return (
            <button
              key={item.key}
              className={`${styles.navItem} ${isActive ? 'active' : ''}`}
              onClick={() => handleNavigation(item.path)}
            >
              <IconComponent className="icon" />
              <span className="text">{item.text}</span>
            </button>
          );
        })}
      </div>

      {/* 底部用户信息 */}
      <div className={styles.footer}>
        <div className={styles.userInfo}>
          <Avatar size={40} src={user?.avatar || '/favicon.png'}>
            {user?.username?.charAt(0).toUpperCase() || 'U'}
          </Avatar>
          <div className="info">
            <p className="name">{user?.username || '用户'}</p>
            <p className="email">{user?.email || '未设置邮箱'}</p>
          </div>
        </div>
        
        <button className={styles.logoutButton} onClick={handleLogout}>
          <LogOut size={16} />
          <span>退出登录</span>
        </button>
      </div>
    </Drawer>
  );
};

export default MobileNavDrawer;
