import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import { generateId } from '@/lib/utils';
import crypto from 'crypto';

// 加密密钥 - 在生产环境中应该从环境变量获取
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'your-32-character-secret-key-here';
const ALGORITHM = 'aes-256-cbc';

// 加密函数
function encrypt(text: string): string {
  if (!text) return '';
  try {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(ALGORITHM, Buffer.from(ENCRYPTION_KEY.slice(0, 32)), iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('hex') + ':' + encrypted;
  } catch (error) {
    console.error('加密失败:', error);
    return text; // 如果加密失败，返回原文本
  }
}

// 解密函数
function decrypt(text: string): string {
  if (!text) return '';
  try {
    const textParts = text.split(':');
    if (textParts.length !== 2) return text; // 如果格式不正确，返回原文本

    const iv = Buffer.from(textParts[0], 'hex');
    const encryptedText = textParts[1];
    const decipher = crypto.createDecipheriv(ALGORITHM, Buffer.from(ENCRYPTION_KEY.slice(0, 32)), iv);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch (error) {
    console.error('解密失败:', error);
    return text; // 如果解密失败，返回原文本
  }
}

// GET - 获取AI提供商配置（支持全局配置）
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const global = searchParams.get('global'); // 新增：支持获取全局配置

    // 如果请求全局配置，查询所有启用的配置（每个提供商只取最新的一个）
    if (global === 'true') {
      const configs = await executeQuery<any[]>(
        `SELECT * FROM ai_provider_configs a1
         WHERE a1.enabled = 1
         AND a1.updated_at = (
           SELECT MAX(a2.updated_at)
           FROM ai_provider_configs a2
           WHERE a2.provider = a1.provider AND a2.enabled = 1
         )
         ORDER BY a1.provider`
      );

      // 解密API密钥并转换字段名为驼峰格式
      const decryptedConfigs = configs.map(config => ({
        id: config.id,
        userId: config.user_id,
        provider: config.provider,
        enabled: Boolean(config.enabled),
        apiKey: decrypt(config.api_key || ''),
        baseUrl: config.base_url,
        defaultModel: config.default_model,
        temperature: config.temperature,
        maxTokens: config.max_tokens,
        extraConfig: (() => {
          try {
            if (!config.extra_config) return null;
            if (typeof config.extra_config === 'object') return config.extra_config;
            if (config.extra_config === '[object Object]') return null;
            return JSON.parse(config.extra_config);
          } catch (error) {
            console.warn('解析extraConfig失败:', config.extra_config, error);
            return null;
          }
        })(),
        createdAt: config.created_at,
        updatedAt: config.updated_at,
      }));

      return NextResponse.json({
        success: true,
        data: decryptedConfigs,
      });
    }

    // 原有逻辑：获取特定用户的配置（用于管理员设置页面）
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: '用户ID为必填项',
        },
        { status: 400 }
      );
    }

    const configs = await executeQuery<any[]>(
      'SELECT * FROM ai_provider_configs WHERE user_id = ? ORDER BY provider',
      [userId]
    );

    // 解密API密钥并转换字段名为驼峰格式
    const decryptedConfigs = configs.map(config => ({
      id: config.id,
      userId: config.user_id,
      provider: config.provider,
      enabled: Boolean(config.enabled),
      apiKey: decrypt(config.api_key || ''),
      baseUrl: config.base_url,
      defaultModel: config.default_model,
      temperature: config.temperature,
      maxTokens: config.max_tokens,
      extraConfig: (() => {
        try {
          if (!config.extra_config) return null;
          if (typeof config.extra_config === 'object') return config.extra_config;
          if (config.extra_config === '[object Object]') return null;
          return JSON.parse(config.extra_config);
        } catch (error) {
          console.warn('解析extraConfig失败:', config.extra_config, error);
          return null;
        }
      })(),
      createdAt: config.created_at,
      updatedAt: config.updated_at,
    }));

    return NextResponse.json({
      success: true,
      data: decryptedConfigs,
    });
  } catch (error) {
    console.error('获取AI提供商配置失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '获取配置失败',
      },
      { status: 500 }
    );
  }
}

// POST - 创建或更新AI提供商配置
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      userId,
      provider,
      enabled = false,
      apiKey = '',
      baseUrl = '',
      defaultModel = '',
      temperature = 0.7,
      maxTokens = 4096,
      extraConfig = null,
    } = body;

    if (!userId || !provider) {
      return NextResponse.json(
        {
          success: false,
          error: '用户ID和提供商为必填项',
        },
        { status: 400 }
      );
    }

    // 检查是否已存在配置
    const existingConfigs = await executeQuery<any[]>(
      'SELECT id FROM ai_provider_configs WHERE user_id = ? AND provider = ?',
      [userId, provider]
    );

    const encryptedApiKey = encrypt(apiKey);
    const extraConfigJson = extraConfig ? JSON.stringify(extraConfig) : null;

    if (existingConfigs.length > 0) {
      // 更新现有配置
      const query = `
        UPDATE ai_provider_configs 
        SET enabled = ?, api_key = ?, base_url = ?, default_model = ?, 
            temperature = ?, max_tokens = ?, extra_config = ?, updated_at = ?
        WHERE user_id = ? AND provider = ?
      `;

      const params = [
        Boolean(enabled),
        encryptedApiKey,
        baseUrl,
        defaultModel,
        temperature,
        maxTokens,
        extraConfigJson,
        new Date(),
        userId,
        provider,
      ];

      await executeQuery(query, params);
    } else {
      // 创建新配置
      const id = generateId();
      const now = new Date();

      const query = `
        INSERT INTO ai_provider_configs (
          id, user_id, provider, enabled, api_key, base_url, default_model,
          temperature, max_tokens, extra_config, created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        id,
        userId,
        provider,
        Boolean(enabled),
        encryptedApiKey,
        baseUrl,
        defaultModel,
        temperature,
        maxTokens,
        extraConfigJson,
        now,
        now,
      ];

      await executeQuery(query, params);
    }

    return NextResponse.json({
      success: true,
      message: '配置保存成功',
    });
  } catch (error) {
    console.error('保存AI提供商配置失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '保存配置失败',
      },
      { status: 500 }
    );
  }
}

// DELETE - 删除AI提供商配置
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const provider = searchParams.get('provider');

    if (!userId || !provider) {
      return NextResponse.json(
        {
          success: false,
          error: '用户ID和提供商为必填项',
        },
        { status: 400 }
      );
    }

    await executeQuery(
      'DELETE FROM ai_provider_configs WHERE user_id = ? AND provider = ?',
      [userId, provider]
    );

    return NextResponse.json({
      success: true,
      message: '配置删除成功',
    });
  } catch (error) {
    console.error('删除AI提供商配置失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '删除配置失败',
      },
      { status: 500 }
    );
  }
}
