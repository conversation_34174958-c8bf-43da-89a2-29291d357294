'use client';

import { SideNav } from '@lobehub/ui';
import { useTheme, createStyles } from 'antd-style';
import { memo } from 'react';

import MainSideBarTopActions from './MainSideBarTopActions';
import MainSideBarBottomActions from './MainSideBarBottomActions';
import MainSideBarAvatar from './MainSideBarAvatar';

const useStyles = createStyles(({ css }) => ({
  sideNav: css`
    @media (max-width: 768px) {
      display: none;
    }
  `,
}));

const MainSideBar = memo(() => {
  const theme = useTheme();
  const { styles } = useStyles();

  return (
    <div className={styles.sideNav}>
      <SideNav
        avatar={<MainSideBarAvatar />}
        bottomActions={<MainSideBarBottomActions />}
        style={{
          height: '100vh',
          minHeight: '100vh',
          position: 'sticky',
          top: 0,
          zIndex: 100,
          background: theme.colorBgLayout,
        }}
        topActions={<MainSideBarTopActions />}
      />
    </div>
  );
});

MainSideBar.displayName = 'MainSideBar';

export default MainSideBar;
