'use client';

import React, { useState, useRef } from 'react';
import { createStyles } from 'antd-style';
import { Button, Input, Upload, Tooltip } from 'antd';
import { SendHorizontal, Paperclip, Mic, Square } from 'lucide-react';
import { message } from '@/components/AntdStaticMethods';
import { FileInfo } from '@/types';

const { TextArea } = Input;

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    padding: ${token.paddingLG}px;
    background: ${token.colorBgContainer};
    border-top: 1px solid ${token.colorBorder};
  `,
  
  inputWrapper: css`
    position: relative;
    background: ${token.colorBgLayout};
    border-radius: ${token.borderRadiusLG}px;
    border: 1px solid ${token.colorBorder};
    transition: all 0.2s;
    
    &:hover {
      border-color: ${token.colorPrimary};
    }
    
    &.focused {
      border-color: ${token.colorPrimary};
      box-shadow: 0 0 0 2px ${token.colorPrimary}14;
    }
  `,
  
  textArea: css`
    .ant-input {
      border: none;
      background: transparent;
      resize: none;
      padding: ${token.paddingMD}px ${token.paddingLG}px;
      padding-right: 120px;
      min-height: 52px;
      max-height: 200px;
      line-height: 1.5;
      
      &:focus {
        box-shadow: none;
      }
      
      &::placeholder {
        color: ${token.colorTextTertiary};
      }
    }
  `,
  
  actions: css`
    position: absolute;
    right: ${token.paddingSM}px;
    bottom: ${token.paddingSM}px;
    display: flex;
    align-items: center;
    gap: ${token.marginXS}px;
  `,
  
  actionButton: css`
    border: none;
    box-shadow: none;
    padding: 8px;
    height: 36px;
    min-width: 36px;
    border-radius: ${token.borderRadius}px;
    
    &:hover {
      background: ${token.colorFillTertiary};
    }
    
    &.primary {
      background: ${token.colorPrimary};
      color: ${token.colorWhite};
      min-width: 60px;
      padding: 8px 12px;

      &:hover {
        background: ${token.colorPrimaryHover};
      }

      &:disabled {
        background: ${token.colorBgTextActive};
        color: ${token.colorTextDisabled};
      }
    }
  `,
  
  fileList: css`
    padding: ${token.paddingSM}px ${token.paddingLG}px 0;
    display: flex;
    flex-wrap: wrap;
    gap: ${token.marginSM}px;
  `,
  
  fileItem: css`
    display: flex;
    align-items: center;
    gap: ${token.marginXS}px;
    background: ${token.colorFillTertiary};
    padding: ${token.paddingXS}px ${token.paddingSM}px;
    border-radius: ${token.borderRadius}px;
    font-size: ${token.fontSizeSM}px;
    
    .file-name {
      max-width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .remove-btn {
      cursor: pointer;
      color: ${token.colorTextTertiary};
      padding: 2px;
      border-radius: 2px;
      
      &:hover {
        background: ${token.colorErrorBg};
        color: ${token.colorError};
      }
    }
  `,
  
  shortcuts: css`
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: ${token.marginSM}px;
    font-size: ${token.fontSizeSM}px;
    color: ${token.colorTextTertiary};
    
    .shortcut-hint {
      display: flex;
      gap: ${token.marginLG}px;
      
      span {
        display: flex;
        align-items: center;
        gap: 4px;
      }
      
      kbd {
        background: ${token.colorFillTertiary};
        padding: 2px 6px;
        border-radius: 4px;
        font-family: monospace;
        font-size: 11px;
      }
    }
  `,
  
  recording: css`
    display: flex;
    align-items: center;
    gap: ${token.marginSM}px;
    color: ${token.colorError};
    
    .recording-dot {
      width: 8px;
      height: 8px;
      background: ${token.colorError};
      border-radius: 50%;
      animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
  `,
}));

interface ChatInputProps {
  onSend: (content: string, files?: FileInfo[]) => void;
  loading?: boolean;
  placeholder?: string;
  maxFiles?: number;
  acceptedFileTypes?: string[];
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSend,
  loading = false,
  placeholder = '输入消息... (Shift + Enter 换行，Enter 发送)',
  maxFiles = 5,
  acceptedFileTypes = ['image/*', '.txt', '.pdf', '.doc', '.docx'],
}) => {
  const { styles } = useStyles();
  const [content, setContent] = useState('');
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [focused, setFocused] = useState(false);
  const [recording, setRecording] = useState(false);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  const handleSend = () => {
    if (!content.trim() && files.length === 0) return;
    
    onSend(content.trim(), files.length > 0 ? files : undefined);
    setContent('');
    setFiles([]);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleFileUpload = (file: File) => {
    if (files.length >= maxFiles) {
      message.warning(`最多只能上传 ${maxFiles} 个文件`);
      return false;
    }

    const fileInfo: FileInfo = {
      id: Date.now().toString(),
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'selected',
    };

    setFiles(prev => [...prev, fileInfo]);
    return false; // 阻止默认上传行为
  };

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const toggleRecording = () => {
    if (recording) {
      // 停止录音
      setRecording(false);
      // TODO: 实现语音识别
      message.info('语音功能开发中...');
    } else {
      // 开始录音
      setRecording(true);
      // TODO: 实现语音录制
    }
  };

  const canSend = (content.trim().length > 0 || files.length > 0) && !loading;

  return (
    <div className={styles.container}>
      <div className={`${styles.inputWrapper} ${focused ? 'focused' : ''}`}>
        {/* 文件列表 */}
        {files.length > 0 && (
          <div className={styles.fileList}>
            {files.map((file) => (
              <div key={file.id} className={styles.fileItem}>
                <span className="file-name">{file.name}</span>
                <span 
                  className="remove-btn"
                  onClick={() => removeFile(file.id)}
                >
                  ×
                </span>
              </div>
            ))}
          </div>
        )}
        
        {/* 输入框 */}
        <div className={styles.textArea}>
          <TextArea
            ref={textAreaRef}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onKeyDown={handleKeyDown}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            placeholder={placeholder}
            autoSize={{ minRows: 1, maxRows: 8 }}
            disabled={loading}
          />
        </div>
        
        {/* 操作按钮 */}
        <div className={styles.actions}>
          {/* 文件上传 */}
          <Upload
            beforeUpload={handleFileUpload}
            showUploadList={false}
            accept={acceptedFileTypes.join(',')}
            disabled={loading}
          >
            <Tooltip title="上传文件">
              <Button
                type="text"
                icon={<Paperclip size={16} />}
                className={styles.actionButton}
                disabled={loading}
              />
            </Tooltip>
          </Upload>
          
          {/* 语音输入 */}
          <Tooltip title={recording ? '停止录音' : '语音输入'}>
            <Button
              type="text"
              icon={recording ? <Square size={16} /> : <Mic size={16} />}
              className={`${styles.actionButton} ${recording ? 'recording' : ''}`}
              onClick={toggleRecording}
              disabled={loading}
            />
          </Tooltip>
          
          {/* 发送按钮 */}
          <Tooltip title="发送消息">
            <Button
              type="primary"
              icon={<SendHorizontal size={16} />}
              className={`${styles.actionButton} primary`}
              onClick={handleSend}
              disabled={!canSend}
              loading={loading}
            >
              发送
            </Button>
          </Tooltip>
        </div>
      </div>
      
      {/* 快捷键提示 */}
      <div className={styles.shortcuts}>
        <div className="shortcut-hint">
          <span>
            <kbd>Enter</kbd> 发送
          </span>
          <span>
            <kbd>Shift</kbd> + <kbd>Enter</kbd> 换行
          </span>
        </div>
        
        {recording && (
          <div className={styles.recording}>
            <div className="recording-dot" />
            <span>正在录音...</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatInput;