'use client';

import React, { useMemo } from 'react';
import { Card, Tag, Empty, Button } from 'antd';
import { Plus, MoreHorizontal } from 'lucide-react';
import { createStyles } from 'antd-style';
import { useAgentStore } from '@/store/agent';
import { Agent, AGENT_CATEGORIES } from '@/types/agent';

const { Meta } = Card;

const useStyles = createStyles(({ css, token }) => ({
  container: css`
    height: 100%;
    overflow-y: auto;
    padding: 16px;

    @media (max-width: 768px) {
      padding: 8px;
      height: auto;
      overflow-y: visible;
    }
  `,
  gridContainer: css`
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    padding: 0;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  `,
  agentCard: css`
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 1px solid ${token.colorBorderSecondary};

    &:hover {
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      transform: translateY(-4px);
      border-color: ${token.colorPrimary};
    }

    .ant-card-body {
      padding: 16px;
    }
  `,
  agentHeader: css`
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
  `,
  agentAvatar: css`
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: ${token.colorBgContainer};
    border: 1px solid ${token.colorBorderSecondary};
    flex-shrink: 0;
  `,

  avatarIcon: css`
    width: 20px;
    height: 20px;
    object-fit: contain;
  `,

  agentActions: css`
    display: flex;
    gap: 8px;
    flex-shrink: 0;
  `,
  agentInfo: css`
    flex: 1;
    min-width: 0;
  `,
  agentName: css`
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: ${token.colorText};
    display: flex;
    align-items: center;
    gap: 8px;
  `,
  agentAuthor: css`
    font-size: 12px;
    color: ${token.colorTextTertiary};
    margin: 0;
  `,
  agentDescription: css`
    font-size: 13px;
    color: ${token.colorTextSecondary};
    margin: 8px 0 12px 0;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  `,

  agentFooter: css`
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid ${token.colorBorderSecondary};
  `,
  agentTags: css`
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    flex: 1;
  `,
  categoryTag: css`
    border-radius: 6px;
    font-size: 11px;
    padding: 2px 6px;
    border: none;
  `,

  githubIcon: css`
    color: ${token.colorTextTertiary};
    cursor: pointer;

    &:hover {
      color: ${token.colorText};
    }
  `,
  emptyContainer: css`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    text-align: center;
  `,
}));

interface AgentListProps {
  onAddToSession?: (agentId: string) => void;
}

const AgentList: React.FC<AgentListProps> = ({ onAddToSession }) => {
  const { styles } = useStyles();
  const { agents, selectedCategory, searchQuery, addAgentToSession } = useAgentStore();



  // 过滤智能体
  const filteredAgents = useMemo(() => {
    let filtered = agents;

    // 按分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(agent => agent.category === selectedCategory);
    }

    // 按搜索查询过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(agent =>
        agent.name.toLowerCase().includes(query) ||
        agent.description?.toLowerCase().includes(query)
      );
    }

    // 按使用次数排序
    return filtered.sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0));
  }, [agents, selectedCategory, searchQuery]);

  // 获取分类信息
  const getCategoryInfo = (category: string) => {
    return AGENT_CATEGORIES.find(cat => cat.key === category);
  };
  // 处理添加到会话
  const handleAddToSession = async (agentId: string) => {
    try {
      await addAgentToSession(agentId);
      onAddToSession?.(agentId);
    } catch (error) {
      console.error('添加智能体到会话失败:', error);
    }
  };





  if (filteredAgents.length === 0) {
    return (
      <div className={styles.emptyContainer}>
        <Empty
          image={<MoreHorizontal size={64} />}
          description={
            searchQuery ? '没有找到匹配的智能体' : '暂无智能体'
          }
        />
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.gridContainer}>
        {filteredAgents.map((agent) => {
          const categoryInfo = getCategoryInfo(agent.category);

          return (
            <Card
              key={agent.id}
              className={styles.agentCard}
              size="small"
            >
              <div className={styles.agentHeader}>
                <div className={styles.agentAvatar}>
                  <img
                    src={agent.avatar || "/favicon.png"}
                    alt={agent.name}
                    className={styles.avatarIcon}
                  />
                </div>
                <div className={styles.agentInfo}>
                  <h4 className={styles.agentName}>
                    {agent.name}
                  </h4>
                </div>
              </div>

              {agent.description && (
                <p className={styles.agentDescription}>
                  {agent.description}
                </p>
              )}



              <div className={styles.agentFooter}>
                <div className={styles.agentTags}>
                  {categoryInfo && (
                    <Tag
                      className={styles.categoryTag}
                      color="blue"
                    >
                      {categoryInfo.label}
                    </Tag>
                  )}
                </div>

                <div className={styles.agentActions}>
                  <Button
                    type="primary"
                    size="small"
                    icon={<Plus size={14} />}
                    onClick={() => handleAddToSession(agent.id)}
                  >
                    添加
                  </Button>
                </div>
              </div>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default AgentList;
