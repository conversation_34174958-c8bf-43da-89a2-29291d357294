import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    // 执行数据库更新
    const updates = [
      // 更新智能体表，添加缺失的字段
      `ALTER TABLE agents ADD COLUMN enabled BOOLEAN DEFAULT TRUE`,
      `ALTER TABLE agents ADD COLUMN coze_api_key TEXT`,
      `ALTER TABLE agents ADD COLUMN coze_bot_id VARCHAR(255)`,
      `ALTER TABLE agents ADD COLUMN coze_user_id VARCHAR(255)`,
      `ALTER TABLE agents ADD COLUMN purchase_link VARCHAR(500)`,
      
      // 创建智能体定价模式表
      `CREATE TABLE IF NOT EXISTS agent_pricing_plans (
        id VARCHAR(50) PRIMARY KEY,
        agent_id VARCHAR(50) NOT NULL,
        name VARCHAR(255) NOT NULL,
        type ENUM('per_usage', 'time_based') NOT NULL,
        usage_count INT NULL,
        price_per_usage DECIMAL(10,2) NULL,
        duration_days INT NULL,
        price_per_period DECIMAL(10,2) NULL,
        currency VARCHAR(10) DEFAULT 'CNY',
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_agent_id (agent_id),
        INDEX idx_type (type),
        INDEX idx_is_active (is_active),
        FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,
      
      // 创建用户智能体订阅表
      `CREATE TABLE IF NOT EXISTS user_agent_subscriptions (
        id VARCHAR(50) PRIMARY KEY,
        user_id VARCHAR(50) NOT NULL,
        agent_id VARCHAR(50) NOT NULL,
        pricing_plan_id VARCHAR(50) NOT NULL,
        remaining_usage INT DEFAULT 0,
        total_usage INT DEFAULT 0,
        start_date TIMESTAMP NULL,
        end_date TIMESTAMP NULL,
        status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
        purchase_price DECIMAL(10,2) NOT NULL,
        currency VARCHAR(10) DEFAULT 'CNY',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_agent_id (agent_id),
        INDEX idx_pricing_plan_id (pricing_plan_id),
        INDEX idx_status (status),
        INDEX idx_end_date (end_date),
        UNIQUE KEY unique_user_agent_plan (user_id, agent_id, pricing_plan_id),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
        FOREIGN KEY (pricing_plan_id) REFERENCES agent_pricing_plans(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,
      
      // 创建智能体使用记录表
      `CREATE TABLE IF NOT EXISTS agent_usage_logs (
        id VARCHAR(50) PRIMARY KEY,
        user_id VARCHAR(50) NOT NULL,
        agent_id VARCHAR(50) NOT NULL,
        subscription_id VARCHAR(50),
        session_id VARCHAR(50),
        usage_type ENUM('chat', 'generation') DEFAULT 'chat',
        tokens_used INT DEFAULT 0,
        cost DECIMAL(10,4) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_agent_id (agent_id),
        INDEX idx_subscription_id (subscription_id),
        INDEX idx_session_id (session_id),
        INDEX idx_created_at (created_at),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
        FOREIGN KEY (subscription_id) REFERENCES user_agent_subscriptions(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

      // 创建兑换码批次表
      `CREATE TABLE IF NOT EXISTS redemption_batches (
        id VARCHAR(50) PRIMARY KEY,
        agent_id VARCHAR(50) NOT NULL,
        batch_name VARCHAR(255) NOT NULL,
        code_type ENUM('duration', 'usage') NOT NULL,
        label VARCHAR(255) NOT NULL,
        total_codes INT NOT NULL,
        used_codes INT DEFAULT 0,
        duration_days INT NULL,
        usage_count INT NULL,
        expires_at TIMESTAMP NULL,
        created_by VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_agent_id (agent_id),
        INDEX idx_code_type (code_type),
        INDEX idx_created_by (created_by),
        INDEX idx_expires_at (expires_at),
        FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

      // 创建兑换码表
      `CREATE TABLE IF NOT EXISTS redemption_codes (
        id VARCHAR(50) PRIMARY KEY,
        agent_id VARCHAR(50) NOT NULL,
        batch_id VARCHAR(50) NULL,
        code VARCHAR(100) NOT NULL UNIQUE,
        code_type ENUM('duration', 'usage') NOT NULL,
        label VARCHAR(255) NOT NULL,
        duration_days INT NULL,
        usage_count INT NULL,
        hash_code VARCHAR(255) NOT NULL,
        salt VARCHAR(100) NOT NULL,
        is_used BOOLEAN DEFAULT FALSE,
        used_at TIMESTAMP NULL,
        used_by VARCHAR(50) NULL,
        expires_at TIMESTAMP NULL,
        is_voided BOOLEAN DEFAULT FALSE,
        voided_at TIMESTAMP NULL,
        voided_by VARCHAR(50) NULL,
        created_by VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_agent_id (agent_id),
        INDEX idx_batch_id (batch_id),
        INDEX idx_code (code),
        INDEX idx_code_type (code_type),
        INDEX idx_created_by (created_by),
        INDEX idx_expires_at (expires_at),
        INDEX idx_is_used (is_used),
        INDEX idx_is_voided (is_voided),
        FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
        FOREIGN KEY (batch_id) REFERENCES redemption_batches(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL,
        FOREIGN KEY (voided_by) REFERENCES users(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

      // 创建兑换码使用记录表
      `CREATE TABLE IF NOT EXISTS redemption_logs (
        id VARCHAR(50) PRIMARY KEY,
        user_id VARCHAR(50) NOT NULL,
        redemption_code_id VARCHAR(50) NOT NULL,
        agent_id VARCHAR(50) NOT NULL,
        subscription_id VARCHAR(50) NULL,
        redeemed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_redemption_code_id (redemption_code_id),
        INDEX idx_agent_id (agent_id),
        INDEX idx_subscription_id (subscription_id),
        INDEX idx_redeemed_at (redeemed_at),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (redemption_code_id) REFERENCES redemption_codes(id) ON DELETE CASCADE,
        FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
        FOREIGN KEY (subscription_id) REFERENCES user_agent_subscriptions(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,


    ];

    // 执行所有更新
    for (const sql of updates) {
      try {
        await executeQuery(sql);
        console.log('执行成功:', sql.substring(0, 50) + '...');
      } catch (error: any) {
        // 忽略已存在的列、索引、表等错误
        if (!error.message.includes('Duplicate column name') &&
            !error.message.includes('already exists') &&
            !error.message.includes('duplicate column name') &&
            !error.message.includes('Duplicate key name') &&
            !error.message.includes('Table') &&
            !error.message.includes('exists')) {
          console.error('执行失败:', sql.substring(0, 50) + '...', error);
          throw error;
        }
      }
    }

    // 为现有表添加新字段
    const alterTableQueries = [
      // 为兑换码表添加作废相关字段
      `ALTER TABLE redemption_codes
       ADD COLUMN IF NOT EXISTS is_voided BOOLEAN DEFAULT FALSE,
       ADD COLUMN IF NOT EXISTS voided_at TIMESTAMP NULL,
       ADD COLUMN IF NOT EXISTS voided_by VARCHAR(50) NULL`,

      // 添加索引
      `ALTER TABLE redemption_codes
       ADD INDEX IF NOT EXISTS idx_is_voided (is_voided)`,

      // 添加外键约束（如果不存在）
      `ALTER TABLE redemption_codes
       ADD CONSTRAINT IF NOT EXISTS fk_voided_by
       FOREIGN KEY (voided_by) REFERENCES users(id) ON DELETE SET NULL`
    ];

    for (const query of alterTableQueries) {
      try {
        await executeQuery(query);
      } catch (error) {
        console.error('ALTER TABLE 执行失败:', error);
        // 继续执行其他语句，不中断流程
      }
    }

    // 插入示例数据
    const sampleData = [
      `INSERT IGNORE INTO agent_pricing_plans (id, agent_id, name, type, usage_count, price_per_usage, currency, description, is_active) VALUES
       ('pricing_1', 'agent_1', '基础套餐', 'per_usage', 100, 0.10, 'CNY', '100次对话，适合轻度使用', true)`,

      `INSERT IGNORE INTO agent_pricing_plans (id, agent_id, name, type, duration_days, price_per_period, currency, description, is_active) VALUES
       ('pricing_2', 'agent_1', '月度套餐', 'time_based', 30, 29.99, 'CNY', '30天无限使用', true)`,

      // 添加兑换码专用的特殊定价计划
      `INSERT IGNORE INTO agent_pricing_plans (id, agent_id, name, type, duration_days, price_per_period, currency, description, is_active) VALUES
       ('redemption_duration', 'system', '兑换码时长套餐', 'time_based', 0, 0.00, 'CNY', '通过兑换码获得的时长套餐', true)`,

      `INSERT IGNORE INTO agent_pricing_plans (id, agent_id, name, type, usage_count, price_per_usage, currency, description, is_active) VALUES
       ('redemption_usage', 'system', '兑换码次数套餐', 'per_usage', 0, 0.00, 'CNY', '通过兑换码获得的次数套餐', true)`
    ];

    for (const sql of sampleData) {
      try {
        await executeQuery(sql);
      } catch (error) {
        console.error('插入示例数据失败:', error);
      }
    }

    return NextResponse.json({
      success: true,
      message: '数据库更新成功'
    });

  } catch (error) {
    console.error('数据库更新失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '数据库更新失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
