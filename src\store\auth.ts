import { create } from 'zustand';
import { AuthState, User, LoginCredentials } from '@/types';

interface AuthStore extends AuthState {
  // Actions
  login: (credentials: LoginCredentials) => Promise<boolean>;
  loginWithAPI: (email: string, password: string, rememberMe?: boolean) => Promise<boolean>;
  logout: () => void;
  checkLoginStatus: () => Promise<void>;
  setUser: (user: User | null) => void;
  updateUserProfile: (updates: Partial<User>) => Promise<boolean>;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  startTokenRefreshTimer: () => void;
  stopTokenRefreshTimer: () => void;
}

// 模拟的管理员凭据
const ADMIN_CREDENTIALS = [
  {
    username: 'wkhgogogo',
    password: 'Wkh11072975813'
  },
  {
    username: 'weitian',
    password: 'Aa123456'
  }
];

export const useAuthStore = create<AuthStore>()((set, get) => ({
      // Initial state
      isLoggedIn: false,
      user: null,
      token: null,
      loading: false,
      error: null,

      // Actions
      login: async (credentials: LoginCredentials) => {
        set({ loading: true, error: null });

        try {
          // 模拟API调用延迟
          await new Promise(resolve => setTimeout(resolve, 1000));

          // 验证凭据
          const isValid = ADMIN_CREDENTIALS.some(
            admin => admin.username === credentials.username && admin.password === credentials.password
          );

          if (isValid) {
            const user: User = {
              id: Date.now().toString(),
              username: credentials.username,
              email: '', // 初始为空，用户可以在个人信息中设置
              avatar: '/favicon.png', // 默认头像
              role: 'admin', // 硬编码的管理员凭据都是管理员角色
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            };

            const token = `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            set({
              isLoggedIn: true,
              user,
              token,
              loading: false,
              error: null,
            });

            // 保存登录状态到localStorage
            // 如果选择记住登录状态，设置为true，否则设置为false但仍然保存登录时间
            localStorage.setItem('gemini_remember_me', credentials.rememberMe ? 'true' : 'false');
            localStorage.setItem('gemini_login_time', new Date().toISOString());
            // 保存用户和token信息
            localStorage.setItem('gemini_user', JSON.stringify(user));
            localStorage.setItem('gemini_token', token);

            return true;
          } else {
            set({
              loading: false,
              error: '用户名或密码错误',
            });
            return false;
          }
        } catch {
          set({
            loading: false,
            error: '登录失败，请重试',
          });
          return false;
        }
      },

      logout: () => {
        // 停止token刷新定时器
        get().stopTokenRefreshTimer();

        set({
          isLoggedIn: false,
          user: null,
          token: null,
          error: null,
        });

        // 清除新的认证信息
        localStorage.removeItem('auth_token');
        localStorage.removeItem('auth_user');
        localStorage.removeItem('auth_remember_me');
        localStorage.removeItem('auth_login_time');

        // 清除旧的认证信息（兼容性）
        localStorage.removeItem('gemini_remember_me');
        localStorage.removeItem('gemini_login_time');
        localStorage.removeItem('gemini_user');
        localStorage.removeItem('gemini_token');
      },

      checkLoginStatus: async () => {
        // 检查新的认证token
        const authToken = localStorage.getItem('auth_token');
        const authUser = localStorage.getItem('auth_user');
        const rememberMe = localStorage.getItem('auth_remember_me') === 'true';
        const loginTime = localStorage.getItem('auth_login_time');

        if (authToken && authUser && loginTime) {
          const loginDate = new Date(loginTime);
          const now = new Date();
          const daysDiff = (now.getTime() - loginDate.getTime()) / (1000 * 60 * 60 * 24);

          if (rememberMe) {
            // 如果选择了记住登录，在7天内保持登录状态
            if (daysDiff <= 7) {
              try {
                const user = JSON.parse(authUser);
                set({
                  isLoggedIn: true,
                  user: {
                    ...user,
                    createdAt: user.createdAt || new Date().toISOString(),
                    updatedAt: user.updatedAt || new Date().toISOString(),
                  },
                  token: authToken
                });
                // 启动token刷新定时器
                get().startTokenRefreshTimer();
                return;
              } catch (error) {
                console.error('解析用户信息失败:', error);
              }
            }
          } else {
            // 如果没有选择记住登录，在1小时内保持登录状态
            const hoursDiff = (now.getTime() - loginDate.getTime()) / (1000 * 60 * 60);
            if (hoursDiff <= 1) {
              try {
                const user = JSON.parse(authUser);
                set({
                  isLoggedIn: true,
                  user: {
                    ...user,
                    createdAt: user.createdAt || new Date().toISOString(),
                    updatedAt: user.updatedAt || new Date().toISOString(),
                  },
                  token: authToken
                });
                // 启动token刷新定时器
                get().startTokenRefreshTimer();
                return;
              } catch (error) {
                console.error('解析用户信息失败:', error);
              }
            }
          }

          // 清除过期的认证信息
          localStorage.removeItem('auth_token');
          localStorage.removeItem('auth_user');
          localStorage.removeItem('auth_remember_me');
          localStorage.removeItem('auth_login_time');
        }

        // 兼容旧的认证系统
        const oldRememberMe = localStorage.getItem('gemini_remember_me') === 'true';
        const oldLoginTime = localStorage.getItem('gemini_login_time');

        if (oldLoginTime) {
          const loginDate = new Date(oldLoginTime);
          const now = new Date();
          const daysDiff = (now.getTime() - loginDate.getTime()) / (1000 * 60 * 60 * 24);

          if (oldRememberMe && daysDiff <= 7) {
            const savedUser = localStorage.getItem('gemini_user');
            const savedToken = localStorage.getItem('gemini_token');
            if (savedUser && savedToken) {
              try {
                const user = JSON.parse(savedUser);
                set({ isLoggedIn: true, user, token: savedToken });
                // 启动token刷新定时器
                get().startTokenRefreshTimer();
                return;
              } catch (error) {
                console.error('解析旧用户信息失败:', error);
              }
            }
          }

          // 清除旧的认证信息
          localStorage.removeItem('gemini_remember_me');
          localStorage.removeItem('gemini_login_time');
          localStorage.removeItem('gemini_user');
          localStorage.removeItem('gemini_token');
        }

        set({ isLoggedIn: false });
      },

      setUser: (user: User | null) => {
        set({ user });
      },

      setLoading: (loading: boolean) => {
        set({ loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      // 新的API登录方法
      loginWithAPI: async (email: string, password: string, rememberMe = false) => {
        set({ loading: true, error: null });

        try {
          const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, password, rememberMe }),
          });

          if (!response.ok) {
            // 处理HTTP错误状态
            let errorMessage = '登录失败';
            try {
              const errorResult = await response.json();
              errorMessage = errorResult.error || errorMessage;
            } catch {
              // 如果无法解析JSON，使用默认错误消息
              errorMessage = `登录失败 (${response.status})`;
            }

            set({
              loading: false,
              error: errorMessage,
            });
            return false;
          }

          const result = await response.json();

          if (result.success) {
            const { user, token } = result.data;

            set({
              isLoggedIn: true,
              user: {
                id: user.id,
                username: user.username,
                email: user.email,
                avatar: user.avatar,
                role: user.role || 'user',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              },
              token,
              loading: false,
              error: null,
            });

            // 保存登录状态到localStorage
            localStorage.setItem('auth_token', token);
            localStorage.setItem('auth_user', JSON.stringify(user));
            localStorage.setItem('auth_remember_me', rememberMe.toString());
            localStorage.setItem('auth_login_time', new Date().toISOString());

            // 启动token刷新定时器
            get().startTokenRefreshTimer();

            return true;
          } else {
            set({
              loading: false,
              error: result.error || '登录失败',
            });
            return false;
          }
        } catch (error) {
          set({
            loading: false,
            error: '网络错误，请重试',
          });
          return false;
        }
      },

      // Token刷新定时器
      startTokenRefreshTimer: () => {
        // 清除现有定时器
        if (typeof window !== 'undefined' && (window as any).tokenRefreshTimer) {
          clearInterval((window as any).tokenRefreshTimer);
        }

        // 每20分钟检查一次token状态
        if (typeof window !== 'undefined') {
          (window as any).tokenRefreshTimer = setInterval(async () => {
            const token = localStorage.getItem('auth_token');
            if (!token) return;

            try {
              // 解析token检查过期时间
              const payload = JSON.parse(atob(token.split('.')[1]));
              const now = Math.floor(Date.now() / 1000);
              const timeUntilExpiry = payload.exp - now;

              // 如果token在30分钟内过期，尝试刷新
              if (timeUntilExpiry < 30 * 60) {
                const response = await fetch('/api/auth/refresh', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                    'x-remember-me': (localStorage.getItem('auth_remember_me') === 'true').toString(),
                  },
                });

                if (response.ok) {
                  const result = await response.json();
                  if (result.success) {
                    localStorage.setItem('auth_token', result.data.token);
                    localStorage.setItem('auth_user', JSON.stringify(result.data.user));
                    localStorage.setItem('auth_login_time', new Date().toISOString());
                    console.log('Token自动刷新成功');
                  }
                }
              }
            } catch (error) {
              console.error('Token检查失败:', error);
            }
          }, 20 * 60 * 1000); // 20分钟
        }
      },

      stopTokenRefreshTimer: () => {
        if (typeof window !== 'undefined' && (window as any).tokenRefreshTimer) {
          clearInterval((window as any).tokenRefreshTimer);
          (window as any).tokenRefreshTimer = null;
        }
      },

      updateUserProfile: async (updates: Partial<User>) => {
        const currentUser = get().user;
        if (!currentUser) {
          set({ error: '用户未登录' });
          return false;
        }

        set({ loading: true, error: null });

        try {
          // 模拟API调用
          await new Promise(resolve => setTimeout(resolve, 1000));

          // 更新用户信息
          const updatedUser: User = {
            ...currentUser,
            ...updates,
            updatedAt: new Date().toISOString(),
          };

          // 更新本地存储
          if (typeof window !== 'undefined') {
            localStorage.setItem('auth_user', JSON.stringify(updatedUser));
          }

          set({
            user: updatedUser,
            loading: false
          });

          return true;
        } catch (error) {
          console.error('更新用户信息失败:', error);
          set({
            error: '更新用户信息失败',
            loading: false
          });
          return false;
        }
      },
    }));