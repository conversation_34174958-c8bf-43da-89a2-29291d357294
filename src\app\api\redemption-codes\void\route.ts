import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// JWT验证中间件
const verifyToken = (request: NextRequest) => {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    return decoded;
  } catch (error) {
    return null;
  }
};

// POST - 作废兑换码
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { codeId } = body;

    if (!codeId) {
      return NextResponse.json(
        {
          success: false,
          error: '兑换码ID为必填项',
        },
        { status: 400 }
      );
    }

    // 查询兑换码信息
    const codeQuery = `
      SELECT id, code, is_used, used_by, used_at, is_voided, code_type, duration_days, usage_count
      FROM redemption_codes
      WHERE id = ?
    `;
    
    const codeResults = await executeQuery<any[]>(codeQuery, [codeId]);
    
    if (codeResults.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '兑换码不存在',
        },
        { status: 404 }
      );
    }

    const redemptionCode = codeResults[0];

    // 检查兑换码是否已经被作废
    if (redemptionCode.is_voided) {
      return NextResponse.json(
        {
          success: false,
          error: '该兑换码已经被作废',
        },
        { status: 400 }
      );
    }

    const now = new Date().toISOString();

    // 如果兑换码已被使用，需要收回权益
    if (redemptionCode.is_used && redemptionCode.used_by) {
      const userId = redemptionCode.used_by;
      
      // 根据兑换码类型收回权益
      if (redemptionCode.code_type === 'duration') {
        // 收回时长权益
        const updateUserQuery = `
          UPDATE user_permissions 
          SET expires_at = CASE 
            WHEN expires_at IS NULL THEN DATE_SUB(NOW(), INTERVAL 1 DAY)
            WHEN expires_at > DATE_ADD(NOW(), INTERVAL ? DAY) THEN DATE_SUB(expires_at, INTERVAL ? DAY)
            ELSE DATE_SUB(NOW(), INTERVAL 1 DAY)
          END,
          updated_at = ?
          WHERE user_id = ? AND agent_id = (SELECT agent_id FROM redemption_codes WHERE id = ?)
        `;
        
        await executeQuery(updateUserQuery, [
          redemptionCode.duration_days,
          redemptionCode.duration_days,
          now,
          userId,
          codeId
        ]);
      } else if (redemptionCode.code_type === 'usage') {
        // 收回次数权益
        const updateUserQuery = `
          UPDATE user_permissions 
          SET usage_count = GREATEST(0, usage_count - ?),
          updated_at = ?
          WHERE user_id = ? AND agent_id = (SELECT agent_id FROM redemption_codes WHERE id = ?)
        `;
        
        await executeQuery(updateUserQuery, [
          redemptionCode.usage_count,
          now,
          userId,
          codeId
        ]);
      }
    }

    // 标记兑换码为作废
    const voidCodeQuery = `
      UPDATE redemption_codes
      SET is_voided = TRUE, voided_at = ?, voided_by = 'admin', updated_at = ?
      WHERE id = ?
    `;

    await executeQuery(voidCodeQuery, [now, now, codeId]);

    return NextResponse.json({
      success: true,
      message: redemptionCode.is_used 
        ? '兑换码已作废，相关权益已收回' 
        : '兑换码已作废',
    });

  } catch (error) {
    console.error('作废兑换码失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '作废兑换码失败',
      },
      { status: 500 }
    );
  }
}
