'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, Spin } from 'antd';
import NoSSR from '@/components/NoSSR';

function HomePageContent() {
  const router = useRouter();

  useEffect(() => {
    const checkAuth = () => {
      try {
        // 检查本地存储的登录状态
        const rememberMe = localStorage.getItem('gemini_remember_me') === 'true';
        const loginTime = localStorage.getItem('gemini_login_time');

        let isLoggedIn = false;

        if (loginTime) {
          const loginDate = new Date(loginTime);
          const now = new Date();
          const daysDiff = (now.getTime() - loginDate.getTime()) / (1000 * 60 * 60 * 24);

          if (rememberMe) {
            // 如果选择了记住登录，在7天内保持登录状态
            if (daysDiff <= 7) {
              isLoggedIn = true;
            } else {
              // 超过7天，清除记住状态
              localStorage.removeItem('gemini_remember_me');
              localStorage.removeItem('gemini_login_time');
            }
          } else {
            // 如果没有选择记住登录，在1小时内保持登录状态
            const hoursDiff = (now.getTime() - loginDate.getTime()) / (1000 * 60 * 60);
            if (hoursDiff <= 1) {
              isLoggedIn = true;
            } else {
              // 超过1小时，清除登录状态
              localStorage.removeItem('gemini_remember_me');
              localStorage.removeItem('gemini_login_time');
            }
          }
        }

        // 延迟一点时间显示加载效果，然后跳转
        setTimeout(() => {
          if (isLoggedIn) {
            router.replace('/chat');
          } else {
            router.replace('/login');
          }
        }, 1000);

      } catch (error) {
        console.error('Auth check error:', error);
        setTimeout(() => {
          router.replace('/login');
        }, 1000);
      }
    };

    checkAuth();
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4">
      <Card className="w-full max-w-sm text-center">
        <div className="py-8">
          <Spin size="large" />
          <p className="mt-4 text-gray-600">正在初始化应用...</p>
        </div>
      </Card>
    </div>
  );
}

export default function HomePage() {
  return (
    <NoSSR fallback={
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4">
        <Card className="w-full max-w-sm text-center">
          <div className="py-8">
            <Spin size="large" />
            <p className="mt-4 text-gray-600">正在加载...</p>
          </div>
        </Card>
      </div>
    }>
      <HomePageContent />
    </NoSSR>
  );
}